import React, { useState, useEffect, useRef } from 'react';
import { TemplateElement } from '../../../types';
import { LineElement } from './LineElement';
import { RectangleElement } from './RectangleElement';
import { CircleElement } from './CircleElement';
import { TextElement } from './TextElement';
import { MultilineTextElement } from './MultilineTextElement';
import { ImageElement } from './ImageElement';
import { QRCodeRenderer } from './QRCodeRenderer';
import { BarcodeRenderer } from './BarcodeRenderer';
import { constrainElementToCanvas } from '../canvasUtils';

interface ElementRendererProps {
  element: TemplateElement;
  isSelected: boolean;
  onSelect: (id: string, e?: React.MouseEvent) => void;  // 修改: 添加事件參數
  onUpdate?: (id: string, updates: Partial<TemplateElement>) => void;
  zoom?: number; // 添加縮放比例參數
  setSelectedTool?: (tool: string | null) => void; // 添加設置選中工具的函數
  // 多選功能 - 新增屬性
  selectedElementIds?: string[];
  moveSelectedElements?: (dx: number, dy: number) => void;
  isMultiMoving?: boolean; // 新增：多選移動狀態標誌
}

export const ElementRenderer: React.FC<ElementRendererProps> = ({ 
  element, 
  isSelected, 
  onSelect,
  onUpdate = () => {}, 
  zoom = 100,
  setSelectedTool,
  selectedElementIds = [],
  moveSelectedElements,
  isMultiMoving = false
}) => {
  // 處理線段元素
  if (element.type === 'line') {
    return (
      <LineElement 
        element={element} 
        isSelected={isSelected} 
        onSelect={onSelect} 
        onUpdate={onUpdate}
        zoom={zoom}
        setSelectedTool={setSelectedTool}
        selectedElementIds={selectedElementIds}
        moveSelectedElements={moveSelectedElements}
        isMultiMoving={isMultiMoving}
      />
    );
  }
  
  // 處理矩形元素和圖標元素
  if (element.type === 'rectangle' || element.type === 'square' || element.type === 'icon') {
    return (
      <RectangleElement 
        element={element} 
        isSelected={isSelected} 
        onSelect={onSelect} 
        onUpdate={onUpdate}
        zoom={zoom}
        setSelectedTool={setSelectedTool}
        selectedElementIds={selectedElementIds}
        moveSelectedElements={moveSelectedElements}
        isMultiMoving={isMultiMoving}
      />
    );
  }
  
  // 處理圓形和橢圓形元素
  if (element.type === 'circle' || element.type === 'ellipse') {
    return (
      <CircleElement 
        element={element} 
        isSelected={isSelected} 
        onSelect={onSelect} 
        onUpdate={onUpdate}
        zoom={zoom}
        setSelectedTool={setSelectedTool}
        selectedElementIds={selectedElementIds}
        moveSelectedElements={moveSelectedElements}
        isMultiMoving={isMultiMoving}
      />
    );
  }

  // 處理單行文字元素
  if (element.type === 'text') {
    return (
      <TextElement 
        element={element} 
        isSelected={isSelected} 
        onSelect={onSelect} 
        onUpdate={onUpdate}
        zoom={zoom}
        setSelectedTool={setSelectedTool}
        selectedElementIds={selectedElementIds}
        moveSelectedElements={moveSelectedElements}
        isMultiMoving={isMultiMoving}
      />
    );
  }

  // 處理多行文字元素
  if (element.type === 'multiline-text') {
    return (
      <MultilineTextElement 
        element={element} 
        isSelected={isSelected} 
        onSelect={onSelect} 
        onUpdate={onUpdate}
        zoom={zoom}
        setSelectedTool={setSelectedTool}
        selectedElementIds={selectedElementIds}
        moveSelectedElements={moveSelectedElements}
        isMultiMoving={isMultiMoving}
      />
    );
  }

  // 處理圖片元素
  if (element.type === 'image') {
    return (
      <ImageElement
        element={element}
        isSelected={isSelected}
        onSelect={onSelect}
        onUpdate={onUpdate}
        zoom={zoom}
        setSelectedTool={setSelectedTool}
        selectedElementIds={selectedElementIds}
        moveSelectedElements={moveSelectedElements}
        isMultiMoving={isMultiMoving}
      />
    );
  }

  // 處理 QR Code 元素
  if (element.type === 'qr-code') {
    return (
      <QRCodeRenderer
        element={element}
        isSelected={isSelected}
        onSelect={onSelect}
        onUpdate={onUpdate}
        zoom={zoom}
        setSelectedTool={setSelectedTool}
        selectedElementIds={selectedElementIds}
        moveSelectedElements={moveSelectedElements}
        isMultiMoving={isMultiMoving}
      />
    );
  }

  // 處理條碼元素
  if (element.type === 'barcode') {
    return (
      <BarcodeRenderer
        element={element}
        isSelected={isSelected}
        onSelect={onSelect}
        onUpdate={onUpdate}
        zoom={zoom}
        setSelectedTool={setSelectedTool}
        selectedElementIds={selectedElementIds}
        moveSelectedElements={moveSelectedElements}
        isMultiMoving={isMultiMoving}
      />
    );
  }

  // 其他未支援的元素類型
  return null;
};