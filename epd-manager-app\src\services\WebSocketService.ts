// EPD Manager App - WebSocket 服務

import {
  Gateway,
  Device,
  DeviceConfig,
  WebSocketMessage,
  ConnectionStatus
} from '../types';
import {
  WEBSOCKET_CONFIG,
  WS_MESSAGE_TYPES,
  CONNECTION_STATUS,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES
} from '../utils/constants';
import {
  generateRandomMac,
  generateBatteryLevel,
  generateRSSI,
  generateImageCode,
  generateDefaultDevices
} from '../utils/generators';
import { getFrontendWebSocketUrl, getWebSocketUrlSync } from '../config/websocket';

// 設備狀態更新事件處理器類型
export type DeviceStatusEventHandler = (event: {
  type: string;
  storeId: string;
  devices: Device[];
  timestamp: string;
  updateType: string;
}) => void;

// 網關狀態更新事件處理器類型
export type GatewayStatusEventHandler = (event: {
  type: string;
  storeId: string;
  gateways: Gateway[];
  timestamp: string;
  updateType: string;
}) => void;

// 門店資料更新事件處理器類型
export type StoreDataUpdateEventHandler = (event: {
  type: string;
  storeId: string;
  storeData: any;
  timestamp: string;
  updateType: string;
}) => void;

// 系統資料更新事件處理器類型
export type SystemDataUpdateEventHandler = (event: {
  type: string;
  systemData: any;
  timestamp: string;
  updateType: string;
}) => void;

// 模板更新事件處理器類型
export type TemplateUpdateEventHandler = (event: {
  type: string;
  storeId: string;
  templates: any[];
  timestamp: string;
  updateType: string;
}) => void;

// 刷圖計畫更新事件處理器類型
export type RefreshPlanUpdateEventHandler = (event: {
  type: string;
  storeId: string;
  refreshPlans: any[];
  timestamp: string;
  updateType: string;
}) => void;

// 批量傳送進度事件處理器類型
export type BatchProgressEventHandler = (event: {
  type: string;
  batchId: string;
  progress: number;
  status: string;
  timestamp: string;
}) => void;

// 前端 WebSocket 客戶端類（用於接收即時更新）
export class FrontendWebSocketClient {
  private ws: WebSocket | null = null;
  private isConnected: boolean = false;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private reconnectTimeout: NodeJS.Timeout | null = null;

  // 事件處理器
  private deviceStatusHandlers: Set<DeviceStatusEventHandler> = new Set();
  private gatewayStatusHandlers: Set<GatewayStatusEventHandler> = new Set();
  private storeDataUpdateHandlers: Set<StoreDataUpdateEventHandler> = new Set();
  private systemDataUpdateHandlers: Set<SystemDataUpdateEventHandler> = new Set();
  private templateUpdateHandlers: Set<TemplateUpdateEventHandler> = new Set();
  private refreshPlanUpdateHandlers: Set<RefreshPlanUpdateEventHandler> = new Set();
  private batchProgressHandlers: Set<BatchProgressEventHandler> = new Set();

  // 訂閱狀態追蹤
  private subscribedStores: Set<string> = new Set();
  private subscribedGatewayStores: Set<string> = new Set();
  private subscribedStoreDataStores: Set<string> = new Set();
  private subscribedTemplateStores: Set<string> = new Set();
  private subscribedRefreshPlanStores: Set<string> = new Set();
  private subscribedBatches: Set<string> = new Set();
  private subscribedSystemData: boolean = false;

  private serverUrl: string | null = null;

  constructor() {}

  // 連接到前端 WebSocket 服務
  async connect(): Promise<boolean> {
    try {
      // 動態獲取用戶設定的 WebSocket URL
      this.serverUrl = await getFrontendWebSocketUrl();
      console.log('嘗試連接到前端 WebSocket 服務:', this.serverUrl);

      this.ws = new WebSocket(this.serverUrl);

      return new Promise((resolve, reject) => {
        if (!this.ws) {
          reject(false);
          return;
        }

        this.ws.onopen = () => {
          console.log('前端 WebSocket 連接已建立');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.setupMessageHandler();

          // 發送客戶端識別
          this.send({
            type: 'client_identify',
            clientType: 'frontend',
            timestamp: new Date().toISOString()
          });

          resolve(true);
        };

        this.ws.onerror = (error) => {
          console.error('前端 WebSocket 連接錯誤:', error);
          reject(false);
        };

        this.ws.onclose = (event) => {
          console.log(`前端 WebSocket 連接已關閉: ${event.code} ${event.reason}`);
          this.handleDisconnection();
        };

        // 設置連接超時
        setTimeout(() => {
          if (!this.isConnected) {
            this.ws?.close();
            reject(false);
          }
        }, 10000); // 10 秒超時
      });
    } catch (error: any) {
      console.error('連接到前端 WebSocket 服務失敗:', error);
      return false;
    }
  }

  // 設置消息處理器
  private setupMessageHandler(): void {
    if (!this.ws) return;

    this.ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        this.handleMessage(message);
      } catch (error) {
        console.error('解析前端 WebSocket 消息失敗:', error);
      }
    };
  }

  // 處理接收到的消息
  private handleMessage(message: any): void {
    console.log('收到前端 WebSocket 消息:', message.type);

    switch (message.type) {
      case 'welcome':
        console.log('收到歡迎消息:', message.message);
        break;
      case 'device_status_subscription_ack':
        console.log(`設備狀態訂閱確認: storeId=${message.storeId}, subscribed=${message.subscribed}`);
        break;
      case 'gateway_status_subscription_ack':
        console.log(`網關狀態訂閱確認: storeId=${message.storeId}, subscribed=${message.subscribed}`);
        break;
      case 'store_data_subscription_ack':
        console.log(`門店資料訂閱確認: storeId=${message.storeId}, subscribed=${message.subscribed}`);
        break;
      case 'system_data_subscription_ack':
        console.log(`系統資料訂閱確認: subscribed=${message.subscribed}`);
        break;
      case 'template_subscription_ack':
        console.log(`模板更新訂閱確認: storeId=${message.storeId}, subscribed=${message.subscribed}`);
        break;
      case 'refresh_plan_subscription_ack':
        console.log(`刷圖計畫訂閱確認: storeId=${message.storeId}, subscribed=${message.subscribed}`);
        break;
      case 'subscription_ack':
        console.log(`批量進度訂閱確認: batchId=${message.batchId}, subscribed=${message.subscribed}`);
        break;
      case 'device_status_update':
        this.handleDeviceStatusUpdate(message);
        break;
      case 'device_crud_update':
        this.handleDeviceStatusUpdate(message);
        break;
      case 'gateway_status_update':
        this.handleGatewayStatusUpdate(message);
        break;
      case 'store_data_update':
        this.handleStoreDataUpdate(message);
        break;
      case 'system_data_update':
        this.handleSystemDataUpdate(message);
        break;
      case 'template_update':
        this.handleTemplateUpdate(message);
        break;
      case 'refresh_plan_update':
        this.handleRefreshPlanUpdate(message);
        break;
      case 'batch_progress':
        this.handleBatchProgress(message);
        break;
      case 'batch_complete':
        this.handleBatchProgress(message);
        break;
      default:
        console.log('未處理的消息類型:', message.type);
    }
  }

  // 處理設備狀態更新
  private handleDeviceStatusUpdate(message: any): void {
    console.log(`收到設備狀態更新: storeId=${message.storeId}, devices=${message.devices?.length || 0}`);

    // 通知所有設備狀態監聽器
    this.deviceStatusHandlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error('設備狀態事件處理器錯誤:', error);
      }
    });
  }

  // 處理網關狀態更新
  private handleGatewayStatusUpdate(message: any): void {
    console.log(`收到網關狀態更新: storeId=${message.storeId}, gateways=${message.gateways?.length || 0}`);

    // 通知所有網關狀態監聽器
    this.gatewayStatusHandlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error('網關狀態事件處理器錯誤:', error);
      }
    });
  }

  // 處理門店資料更新
  private handleStoreDataUpdate(message: any): void {
    console.log(`收到門店資料更新: storeId=${message.storeId}`);

    // 通知所有門店資料更新監聽器
    this.storeDataUpdateHandlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error('門店資料更新事件處理器錯誤:', error);
      }
    });
  }

  // 處理系統資料更新
  private handleSystemDataUpdate(message: any): void {
    console.log('收到系統資料更新');

    // 通知所有系統資料更新監聽器
    this.systemDataUpdateHandlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error('系統資料更新事件處理器錯誤:', error);
      }
    });
  }

  // 處理模板更新
  private handleTemplateUpdate(message: any): void {
    console.log(`收到模板更新: storeId=${message.storeId}, templates=${message.templates?.length || 0}`);

    // 通知所有模板更新監聽器
    this.templateUpdateHandlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error('模板更新事件處理器錯誤:', error);
      }
    });
  }

  // 處理刷圖計畫更新
  private handleRefreshPlanUpdate(message: any): void {
    console.log(`收到刷圖計畫更新: storeId=${message.storeId}, refreshPlans=${message.refreshPlans?.length || 0}`);

    // 通知所有刷圖計畫更新監聽器
    this.refreshPlanUpdateHandlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error('刷圖計畫更新事件處理器錯誤:', error);
      }
    });
  }

  // 處理批量進度更新
  private handleBatchProgress(message: any): void {
    console.log(`收到批量進度更新: batchId=${message.batchId}, progress=${message.progress}%`);

    // 通知所有批量進度監聽器
    this.batchProgressHandlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error('批量進度事件處理器錯誤:', error);
      }
    });
  }

  // 訂閱設備狀態
  subscribeDeviceStatus(storeId: string, options?: any): void {
    if (!this.isConnected) {
      console.warn('WebSocket 未連接，無法訂閱設備狀態');
      return;
    }

    this.subscribedStores.add(storeId);
    console.log(`訂閱設備狀態: storeId=${storeId}`);

    this.send({
      type: 'subscribe_device_status',
      storeId,
      options: options || {
        includeImageStatus: true,
        includeBatteryInfo: true
      },
      timestamp: new Date().toISOString()
    });
  }

  // 取消訂閱設備狀態
  unsubscribeDeviceStatus(storeId: string): void {
    if (!this.isConnected) {
      console.warn('WebSocket 未連接，無法取消訂閱設備狀態');
      return;
    }

    this.subscribedStores.delete(storeId);
    console.log(`取消訂閱設備狀態: storeId=${storeId}`);

    this.send({
      type: 'unsubscribe_device_status',
      storeId,
      timestamp: new Date().toISOString()
    });
  }

  // 訂閱網關狀態
  subscribeGatewayStatus(storeId: string, options?: any): void {
    if (!this.isConnected) {
      console.warn('WebSocket 未連接，無法訂閱網關狀態');
      return;
    }

    this.subscribedGatewayStores.add(storeId);
    console.log(`訂閱網關狀態: storeId=${storeId}`);

    this.send({
      type: 'subscribe_gateway_status',
      storeId,
      options: options || {
        includeConnectionInfo: true,
        includeFirmwareInfo: true
      },
      timestamp: new Date().toISOString()
    });
  }

  // 取消訂閱網關狀態
  unsubscribeGatewayStatus(storeId: string): void {
    if (!this.isConnected) {
      console.warn('WebSocket 未連接，無法取消訂閱網關狀態');
      return;
    }

    this.subscribedGatewayStores.delete(storeId);
    console.log(`取消訂閱網關狀態: storeId=${storeId}`);

    this.send({
      type: 'unsubscribe_gateway_status',
      storeId,
      timestamp: new Date().toISOString()
    });
  }

  // 訂閱門店資料更新
  subscribeStoreDataUpdate(storeId: string, options?: any): void {
    if (!this.isConnected) {
      console.warn('WebSocket 未連接，無法訂閱門店資料更新');
      return;
    }

    this.subscribedStoreDataStores.add(storeId);
    console.log(`訂閱門店資料更新: storeId=${storeId}`);

    this.send({
      type: 'subscribe_store_data_update',
      storeId,
      options: options || {},
      timestamp: new Date().toISOString()
    });
  }

  // 取消訂閱門店資料更新
  unsubscribeStoreDataUpdate(storeId: string): void {
    if (!this.isConnected) {
      console.warn('WebSocket 未連接，無法取消訂閱門店資料更新');
      return;
    }

    this.subscribedStoreDataStores.delete(storeId);
    console.log(`取消訂閱門店資料更新: storeId=${storeId}`);

    this.send({
      type: 'unsubscribe_store_data_update',
      storeId,
      timestamp: new Date().toISOString()
    });
  }

  // 訂閱系統資料更新
  subscribeSystemDataUpdate(options?: any): void {
    if (!this.isConnected) {
      console.warn('WebSocket 未連接，無法訂閱系統資料更新');
      return;
    }

    this.subscribedSystemData = true;
    console.log('訂閱系統資料更新');

    this.send({
      type: 'subscribe_system_data_update',
      options: options || {},
      timestamp: new Date().toISOString()
    });
  }

  // 取消訂閱系統資料更新
  unsubscribeSystemDataUpdate(): void {
    if (!this.isConnected) {
      console.warn('WebSocket 未連接，無法取消訂閱系統資料更新');
      return;
    }

    this.subscribedSystemData = false;
    console.log('取消訂閱系統資料更新');

    this.send({
      type: 'unsubscribe_system_data_update',
      timestamp: new Date().toISOString()
    });
  }

  // 訂閱模板更新
  subscribeTemplateUpdate(storeId: string, options?: any): void {
    if (!this.isConnected) {
      console.warn('WebSocket 未連接，無法訂閱模板更新');
      return;
    }

    this.subscribedTemplateStores.add(storeId);
    console.log(`訂閱模板更新: storeId=${storeId}`);

    this.send({
      type: 'subscribe_template_update',
      storeId,
      options: options || {},
      timestamp: new Date().toISOString()
    });
  }

  // 取消訂閱模板更新
  unsubscribeTemplateUpdate(storeId: string): void {
    if (!this.isConnected) {
      console.warn('WebSocket 未連接，無法取消訂閱模板更新');
      return;
    }

    this.subscribedTemplateStores.delete(storeId);
    console.log(`取消訂閱模板更新: storeId=${storeId}`);

    this.send({
      type: 'unsubscribe_template_update',
      storeId,
      timestamp: new Date().toISOString()
    });
  }

  // 訂閱刷圖計畫更新
  subscribeRefreshPlanUpdate(storeId: string, options?: any): void {
    if (!this.isConnected) {
      console.warn('WebSocket 未連接，無法訂閱刷圖計畫更新');
      return;
    }

    this.subscribedRefreshPlanStores.add(storeId);
    console.log(`訂閱刷圖計畫更新: storeId=${storeId}`);

    this.send({
      type: 'subscribe_refresh_plan_update',
      storeId,
      options: options || {},
      timestamp: new Date().toISOString()
    });
  }

  // 取消訂閱刷圖計畫更新
  unsubscribeRefreshPlanUpdate(storeId: string): void {
    if (!this.isConnected) {
      console.warn('WebSocket 未連接，無法取消訂閱刷圖計畫更新');
      return;
    }

    this.subscribedRefreshPlanStores.delete(storeId);
    console.log(`取消訂閱刷圖計畫更新: storeId=${storeId}`);

    this.send({
      type: 'unsubscribe_refresh_plan_update',
      storeId,
      timestamp: new Date().toISOString()
    });
  }

  // 訂閱批量傳送進度
  subscribeBatchProgress(batchId: string): void {
    if (!this.isConnected) {
      console.warn('WebSocket 未連接，無法訂閱批量傳送進度');
      return;
    }

    this.subscribedBatches.add(batchId);
    console.log(`訂閱批量傳送進度: batchId=${batchId}`);

    this.send({
      type: 'subscribe_batch_progress',
      batchId,
      timestamp: new Date().toISOString()
    });
  }

  // 取消訂閱批量傳送進度
  unsubscribeBatchProgress(batchId: string): void {
    if (!this.isConnected) {
      console.warn('WebSocket 未連接，無法取消訂閱批量傳送進度');
      return;
    }

    this.subscribedBatches.delete(batchId);
    console.log(`取消訂閱批量傳送進度: batchId=${batchId}`);

    this.send({
      type: 'unsubscribe_batch_progress',
      batchId,
      timestamp: new Date().toISOString()
    });
  }

  // 添加設備狀態事件監聽器
  addDeviceStatusListener(handler: DeviceStatusEventHandler): void {
    this.deviceStatusHandlers.add(handler);
  }

  // 移除設備狀態事件監聽器
  removeDeviceStatusListener(handler: DeviceStatusEventHandler): void {
    this.deviceStatusHandlers.delete(handler);
  }

  // 添加網關狀態事件監聽器
  addGatewayStatusListener(handler: GatewayStatusEventHandler): void {
    this.gatewayStatusHandlers.add(handler);
  }

  // 移除網關狀態事件監聽器
  removeGatewayStatusListener(handler: GatewayStatusEventHandler): void {
    this.gatewayStatusHandlers.delete(handler);
  }

  // 添加門店資料更新事件監聽器
  addStoreDataUpdateListener(handler: StoreDataUpdateEventHandler): void {
    this.storeDataUpdateHandlers.add(handler);
  }

  // 移除門店資料更新事件監聽器
  removeStoreDataUpdateListener(handler: StoreDataUpdateEventHandler): void {
    this.storeDataUpdateHandlers.delete(handler);
  }

  // 添加系統資料更新事件監聽器
  addSystemDataUpdateListener(handler: SystemDataUpdateEventHandler): void {
    this.systemDataUpdateHandlers.add(handler);
  }

  // 移除系統資料更新事件監聽器
  removeSystemDataUpdateListener(handler: SystemDataUpdateEventHandler): void {
    this.systemDataUpdateHandlers.delete(handler);
  }

  // 添加模板更新事件監聽器
  addTemplateUpdateListener(handler: TemplateUpdateEventHandler): void {
    this.templateUpdateHandlers.add(handler);
  }

  // 移除模板更新事件監聽器
  removeTemplateUpdateListener(handler: TemplateUpdateEventHandler): void {
    this.templateUpdateHandlers.delete(handler);
  }

  // 添加刷圖計畫更新事件監聽器
  addRefreshPlanUpdateListener(handler: RefreshPlanUpdateEventHandler): void {
    this.refreshPlanUpdateHandlers.add(handler);
  }

  // 移除刷圖計畫更新事件監聽器
  removeRefreshPlanUpdateListener(handler: RefreshPlanUpdateEventHandler): void {
    this.refreshPlanUpdateHandlers.delete(handler);
  }

  // 添加批量進度事件監聽器
  addBatchProgressListener(handler: BatchProgressEventHandler): void {
    this.batchProgressHandlers.add(handler);
  }

  // 移除批量進度事件監聽器
  removeBatchProgressListener(handler: BatchProgressEventHandler): void {
    this.batchProgressHandlers.delete(handler);
  }

  // 發送消息
  private send(message: any): void {
    if (this.ws && this.isConnected) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('前端 WebSocket 未連接，無法發送消息');
    }
  }

  // 處理斷線
  private handleDisconnection(): void {
    this.isConnected = false;

    // 自動重連邏輯
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`嘗試重連前端 WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

      this.reconnectTimeout = setTimeout(() => {
        this.connect();
      }, 5000); // 5秒後重連
    } else {
      console.log('達到最大重連次數，停止重連前端 WebSocket');
    }
  }

  // 斷開連接
  disconnect(): void {
    console.log('主動斷開前端 WebSocket 連接');

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    this.isConnected = false;
    this.reconnectAttempts = this.maxReconnectAttempts; // 阻止自動重連

    // 清理所有訂閱狀態
    this.subscribedStores.clear();
    this.subscribedGatewayStores.clear();
    this.subscribedStoreDataStores.clear();
    this.subscribedTemplateStores.clear();
    this.subscribedRefreshPlanStores.clear();
    this.subscribedBatches.clear();
    this.subscribedSystemData = false;

    // 清理所有事件處理器
    this.deviceStatusHandlers.clear();
    this.gatewayStatusHandlers.clear();
    this.storeDataUpdateHandlers.clear();
    this.systemDataUpdateHandlers.clear();
    this.templateUpdateHandlers.clear();
    this.refreshPlanUpdateHandlers.clear();
    this.batchProgressHandlers.clear();
  }

  // 獲取連接狀態
  getConnectionStatus(): boolean {
    return this.isConnected;
  }
}

export class WebSocketService {
  private ws: WebSocket | null = null;
  private isConnected: boolean = false;
  private connectionStatus: ConnectionStatus = CONNECTION_STATUS.DISCONNECTED;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = WEBSOCKET_CONFIG.MAX_RECONNECT_ATTEMPTS;
  
  // 定時器
  private pingInterval: NodeJS.Timeout | null = null;
  private deviceStatusInterval: NodeJS.Timeout | null = null;
  private gatewayInfoInterval: NodeJS.Timeout | null = null;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  
  // 設備管理
  private customDevices: Device[] = [];
  private deviceImageCodes: Record<string, string> = {};
  
  // 事件監聽器
  private messageHandlers: Map<string, (message: WebSocketMessage) => void> = new Map();
  private statusChangeHandlers: Set<(status: ConnectionStatus) => void> = new Set();
  
  // 當前網關信息
  private currentGateway: Gateway | null = null;
  private currentStoreId: string | null = null;

  constructor() {
    this.setupDefaultMessageHandlers();
  }

  /**
   * 設置默認消息處理器
   */
  private setupDefaultMessageHandlers(): void {
    this.addMessageHandler(WS_MESSAGE_TYPES.WELCOME, this.handleWelcomeMessage.bind(this));
    this.addMessageHandler(WS_MESSAGE_TYPES.PONG, this.handlePongMessage.bind(this));
    this.addMessageHandler(WS_MESSAGE_TYPES.GATEWAY_INFO_ACK, this.handleGatewayInfoAck.bind(this));
    this.addMessageHandler(WS_MESSAGE_TYPES.UPDATE_PREVIEW, this.handleImageUpdate.bind(this));
  }

  /**
   * 連接到網關
   */
  async connectToGateway(gateway: Gateway, storeId: string): Promise<boolean> {
    try {
      this.currentGateway = gateway;
      this.currentStoreId = storeId;
      
      // 檢查 WebSocket 配置
      if (!gateway.websocket) {
        throw new Error('網關缺少 WebSocket 配置');
      }

      this.setConnectionStatus(CONNECTION_STATUS.CONNECTING);

      // 構建 WebSocket URL
      const url = `${gateway.websocket.url}?token=${gateway.websocket.token}`;
      console.log('嘗試連接到 WebSocket:', url);

      this.ws = new WebSocket(url);
      
      return new Promise((resolve, reject) => {
        if (!this.ws) {
          reject(false);
          return;
        }

        this.ws.onopen = () => {
          console.log('WebSocket 連接已建立');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.setConnectionStatus(CONNECTION_STATUS.CONNECTED);
          this.setupMessageHandler();
          resolve(true);
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket 連接錯誤:', error);
          this.setConnectionStatus(CONNECTION_STATUS.ERROR);
          reject(false);
        };

        this.ws.onclose = (event) => {
          console.log(`WebSocket 連接已關閉: ${event.code} ${event.reason}`);
          this.handleDisconnection();
        };

        // 設置連接超時
        setTimeout(() => {
          if (!this.isConnected) {
            this.ws?.close();
            reject(false);
          }
        }, 10000); // 10 秒超時
      });
    } catch (error: any) {
      console.error('連接到網關失敗:', error);
      this.setConnectionStatus(CONNECTION_STATUS.ERROR);
      return false;
    }
  }

  /**
   * 開始網關模擬
   */
  startGatewaySimulation(gateway: Gateway): void {
    if (!this.isConnected || !this.ws) {
      throw new Error('WebSocket 未連接');
    }

    console.log('開始網關模擬行為（僅保持連線，不發送設備報告）');

    // 發送初始 ping 消息
    this.sendPingMessage();

    // 設置定期心跳（25秒間隔）
    this.pingInterval = setInterval(() => {
      this.sendPingMessage();
    }, WEBSOCKET_CONFIG.PING_INTERVAL);

    // 設置定期網關信息發送（30秒間隔）
    this.gatewayInfoInterval = setInterval(() => {
      this.sendGatewayInfoMessage(gateway);
    }, WEBSOCKET_CONFIG.GATEWAY_INFO_INTERVAL);

    // 移除自動發送設備狀態的部分，因為這是模擬 GATEWAY 發送 REPORT
    // 實際的 GATEWAY 會自己發送設備狀態，APP 不應該模擬這個行為
    console.log('注意：已移除自動發送設備狀態報告，等待實際 GATEWAY 連接');
  }

  /**
   * 設置消息處理器
   */
  private setupMessageHandler(): void {
    if (!this.ws) return;

    this.ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data);
        this.handleMessage(message);
      } catch (error) {
        console.error('解析 WebSocket 消息失敗:', error);
      }
    };
  }

  /**
   * 處理接收到的消息
   */
  private handleMessage(message: WebSocketMessage): void {
    console.log('收到 WebSocket 消息:', message.type, message);

    // 調用對應的消息處理器
    const handler = this.messageHandlers.get(message.type);
    if (handler) {
      handler(message);
    } else {
      console.warn('未知的消息類型:', message.type);
    }

    // 調用通用消息處理器
    const allHandler = this.messageHandlers.get('all');
    if (allHandler) {
      allHandler(message);
    }
  }

  /**
   * 處理歡迎消息
   */
  private handleWelcomeMessage(message: WebSocketMessage): void {
    console.log('收到歡迎消息，連接成功建立');
    
    // 收到歡迎消息後立即發送網關信息
    if (this.currentGateway) {
      setTimeout(() => {
        this.sendGatewayInfoMessage(this.currentGateway!);
      }, 1000);
    }
  }

  /**
   * 處理 pong 消息
   */
  private handlePongMessage(message: WebSocketMessage): void {
    console.log('收到服務器 pong 回應');
  }

  /**
   * 處理網關信息確認消息
   */
  private handleGatewayInfoAck(message: WebSocketMessage): void {
    if (message.success === false && message.fatal === true) {
      console.error('嚴重錯誤: 服務器因安全問題強制中斷連線');
      console.error('錯誤原因:', message.message);
      this.disconnect();
    } else if (message.success === false) {
      console.warn('網關信息更新失敗:', message.message);
    } else {
      console.log('網關信息更新成功');
    }
  }

  /**
   * 處理圖像更新消息
   */
  private handleImageUpdate(message: WebSocketMessage): void {
    console.log('收到圖像更新消息');

    if (message.deviceMac && message.imageCode) {
      this.deviceImageCodes[message.deviceMac] = message.imageCode;
      console.log(`更新設備 ${message.deviceMac} 的 imageCode: ${message.imageCode}`);
    }

    // 如果有圖像數據，可以進一步處理
    if (message.imageData) {
      console.log('收到圖像數據，長度:', message.imageData.length);
      // 這裡可以添加圖像保存邏輯
    }
  }

  /**
   * 發送 ping 消息
   */
  private sendPingMessage(): void {
    const pingMessage = {
      type: WS_MESSAGE_TYPES.PING,
      timestamp: Date.now()
    };
    this.sendMessage(pingMessage);
  }

  /**
   * 發送設備狀態消息
   */
  private sendDeviceStatusMessage(): void {
    // 生成預設設備
    const defaultDevices = generateDefaultDevices(3);
    
    // 為預設設備添加 imageCode
    defaultDevices.forEach(device => {
      if (this.deviceImageCodes[device.macAddress]) {
        device.data.imageCode = this.deviceImageCodes[device.macAddress];
      }
    });

    // 處理自定義設備
    const processedCustomDevices = this.customDevices.map(device => {
      const processedDevice = { ...device };
      if (this.deviceImageCodes[device.macAddress]) {
        processedDevice.data = {
          ...processedDevice.data,
          imageCode: this.deviceImageCodes[device.macAddress]
        };
      }
      // 更新動態數據
      processedDevice.data.battery = generateBatteryLevel();
      processedDevice.data.rssi = generateRSSI();
      return processedDevice;
    });

    // 合併所有設備
    const allDevices = [...defaultDevices, ...processedCustomDevices];

    const deviceStatusMessage = {
      type: WS_MESSAGE_TYPES.DEVICE_STATUS,
      devices: allDevices,
      timestamp: Date.now()
    };

    this.sendMessage(deviceStatusMessage);
    console.log(`發送設備狀態消息，共 ${allDevices.length} 個設備`);
  }

  /**
   * 發送網關信息消息
   */
  private sendGatewayInfoMessage(gateway: Gateway): void {
    const gatewayInfoMessage = {
      type: WS_MESSAGE_TYPES.GATEWAY_INFO,
      info: {
        macAddress: gateway.macAddress,
        model: gateway.model || 'GW-2000',
        wifiFirmwareVersion: gateway.wifiFirmwareVersion || '1.0.0',
        btFirmwareVersion: gateway.btFirmwareVersion || '2.0.0',
        ipAddress: gateway.ipAddress
      },
      timestamp: Date.now()
    };

    this.sendMessage(gatewayInfoMessage);
    console.log('發送網關信息消息');
  }

  /**
   * 發送消息到服務器
   */
  private sendMessage(message: any): void {
    if (this.ws && this.isConnected) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket 未連接，無法發送消息');
    }
  }

  /**
   * 添加自定義設備
   */
  addCustomDevice(deviceConfig: DeviceConfig): void {
    const customDevice: Device = {
      macAddress: deviceConfig.macAddress,
      status: deviceConfig.status || 'online',
      data: {
        size: deviceConfig.size || '2.9"',
        battery: generateBatteryLevel(),
        rssi: generateRSSI(),
        colorType: deviceConfig.colorType || 'BW'
      }
    };

    this.customDevices.push(customDevice);
    
    // 如果提供了 imageCode，存儲到本地映射
    if (deviceConfig.imageCode) {
      this.deviceImageCodes[deviceConfig.macAddress] = deviceConfig.imageCode;
    }

    console.log(`添加自定義設備: ${deviceConfig.macAddress}`);
  }

  /**
   * 移除自定義設備
   */
  removeCustomDevice(index: number): boolean {
    if (index >= 0 && index < this.customDevices.length) {
      const removedDevice = this.customDevices.splice(index, 1)[0];
      delete this.deviceImageCodes[removedDevice.macAddress];
      console.log(`移除設備: ${removedDevice.macAddress}`);
      return true;
    }
    return false;
  }

  /**
   * 獲取設備列表
   */
  getDeviceList(): Device[] {
    const defaultDevices = generateDefaultDevices(3);
    return [...defaultDevices, ...this.customDevices];
  }

  /**
   * 請求設備預覽圖像
   */
  requestDeviceImage(macAddress: string): void {
    const requestImageMessage = {
      type: WS_MESSAGE_TYPES.REQUEST_PREVIEW_IMAGE,
      macAddress: macAddress,
      timestamp: Date.now()
    };

    this.sendMessage(requestImageMessage);
    console.log(`請求設備 ${macAddress} 的預覽圖像`);
  }

  /**
   * 添加消息處理器
   */
  addMessageHandler(messageType: string, handler: (message: WebSocketMessage) => void): void {
    this.messageHandlers.set(messageType, handler);
  }

  /**
   * 移除消息處理器
   */
  removeMessageHandler(messageType: string): void {
    this.messageHandlers.delete(messageType);
  }

  /**
   * 添加狀態變化監聽器
   */
  addStatusChangeHandler(handler: (status: ConnectionStatus) => void): void {
    this.statusChangeHandlers.add(handler);
  }

  /**
   * 移除狀態變化監聽器
   */
  removeStatusChangeHandler(handler: (status: ConnectionStatus) => void): void {
    this.statusChangeHandlers.delete(handler);
  }

  /**
   * 設置連接狀態
   */
  private setConnectionStatus(status: ConnectionStatus): void {
    this.connectionStatus = status;
    this.statusChangeHandlers.forEach(handler => handler(status));
  }

  /**
   * 處理斷線
   */
  private handleDisconnection(): void {
    this.isConnected = false;
    this.setConnectionStatus(CONNECTION_STATUS.DISCONNECTED);
    this.cleanup();
    
    // 自動重連邏輯
    if (this.reconnectAttempts < this.maxReconnectAttempts && this.currentGateway && this.currentStoreId) {
      this.reconnectAttempts++;
      console.log(`嘗試重連 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      this.reconnectTimeout = setTimeout(() => {
        this.connectToGateway(this.currentGateway!, this.currentStoreId!);
      }, WEBSOCKET_CONFIG.RECONNECT_INTERVAL);
    } else {
      console.log('達到最大重連次數，停止重連');
      this.setConnectionStatus(CONNECTION_STATUS.ERROR);
    }
  }

  /**
   * 清理資源
   */
  private cleanup(): void {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
    if (this.deviceStatusInterval) {
      clearInterval(this.deviceStatusInterval);
      this.deviceStatusInterval = null;
    }
    if (this.gatewayInfoInterval) {
      clearInterval(this.gatewayInfoInterval);
      this.gatewayInfoInterval = null;
    }
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
  }

  /**
   * 斷開連接
   */
  disconnect(): void {
    console.log('主動斷開 WebSocket 連接');
    this.cleanup();
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    
    this.isConnected = false;
    this.reconnectAttempts = this.maxReconnectAttempts; // 阻止自動重連
    this.setConnectionStatus(CONNECTION_STATUS.DISCONNECTED);
    
    // 清理狀態
    this.currentGateway = null;
    this.currentStoreId = null;
    this.customDevices = [];
    this.deviceImageCodes = {};
  }

  /**
   * 獲取連接狀態
   */
  getConnectionStatus(): ConnectionStatus {
    return this.connectionStatus;
  }

  /**
   * 是否已連接
   */
  isWebSocketConnected(): boolean {
    return this.isConnected;
  }

  /**
   * 獲取當前網關
   */
  getCurrentGateway(): Gateway | null {
    return this.currentGateway;
  }
}

// 創建單例實例（用於網關模擬）
export const webSocketService = new WebSocketService();
export default webSocketService;

// 創建全局前端 WebSocket 客戶端實例（用於接收即時更新）
// 注意：URL 會在連接時動態獲取用戶設定的服務器 IP
export const frontendWebSocketClient = new FrontendWebSocketClient();

// 便捷的設備狀態訂閱方法
export const subscribeToDeviceStatus = (
  storeId: string,
  handler: DeviceStatusEventHandler,
  options?: any
): (() => void) => {
  frontendWebSocketClient.addDeviceStatusListener(handler);
  frontendWebSocketClient.subscribeDeviceStatus(storeId, options);

  // 返回清理函數
  return () => {
    frontendWebSocketClient.removeDeviceStatusListener(handler);
    frontendWebSocketClient.unsubscribeDeviceStatus(storeId);
  };
};

// 便捷的網關狀態訂閱方法
export const subscribeToGatewayStatus = (
  storeId: string,
  handler: GatewayStatusEventHandler,
  options?: any
): (() => void) => {
  frontendWebSocketClient.addGatewayStatusListener(handler);
  frontendWebSocketClient.subscribeGatewayStatus(storeId, options);

  // 返回清理函數
  return () => {
    frontendWebSocketClient.removeGatewayStatusListener(handler);
    frontendWebSocketClient.unsubscribeGatewayStatus(storeId);
  };
};

// 便捷的門店資料更新訂閱方法
export const subscribeToStoreDataUpdate = (
  storeId: string,
  handler: StoreDataUpdateEventHandler,
  options?: any
): (() => void) => {
  frontendWebSocketClient.addStoreDataUpdateListener(handler);
  frontendWebSocketClient.subscribeStoreDataUpdate(storeId, options);

  // 返回清理函數
  return () => {
    frontendWebSocketClient.removeStoreDataUpdateListener(handler);
    frontendWebSocketClient.unsubscribeStoreDataUpdate(storeId);
  };
};

// 便捷的系統資料更新訂閱方法
export const subscribeToSystemDataUpdate = (
  handler: SystemDataUpdateEventHandler,
  options?: any
): (() => void) => {
  frontendWebSocketClient.addSystemDataUpdateListener(handler);
  frontendWebSocketClient.subscribeSystemDataUpdate(options);

  // 返回清理函數
  return () => {
    frontendWebSocketClient.removeSystemDataUpdateListener(handler);
    frontendWebSocketClient.unsubscribeSystemDataUpdate();
  };
};

// 便捷的模板更新訂閱方法
export const subscribeToTemplateUpdate = (
  storeId: string,
  handler: TemplateUpdateEventHandler,
  options?: any
): (() => void) => {
  frontendWebSocketClient.addTemplateUpdateListener(handler);
  frontendWebSocketClient.subscribeTemplateUpdate(storeId, options);

  // 返回清理函數
  return () => {
    frontendWebSocketClient.removeTemplateUpdateListener(handler);
    frontendWebSocketClient.unsubscribeTemplateUpdate(storeId);
  };
};

// 便捷的刷圖計畫更新訂閱方法
export const subscribeToRefreshPlanUpdate = (
  storeId: string,
  handler: RefreshPlanUpdateEventHandler,
  options?: any
): (() => void) => {
  frontendWebSocketClient.addRefreshPlanUpdateListener(handler);
  frontendWebSocketClient.subscribeRefreshPlanUpdate(storeId, options);

  // 返回清理函數
  return () => {
    frontendWebSocketClient.removeRefreshPlanUpdateListener(handler);
    frontendWebSocketClient.unsubscribeRefreshPlanUpdate(storeId);
  };
};

// 便捷的批量進度訂閱方法
export const subscribeToBatchProgress = (
  batchId: string,
  handler: BatchProgressEventHandler
): (() => void) => {
  frontendWebSocketClient.addBatchProgressListener(handler);
  frontendWebSocketClient.subscribeBatchProgress(batchId);

  // 返回清理函數
  return () => {
    frontendWebSocketClient.removeBatchProgressListener(handler);
    frontendWebSocketClient.unsubscribeBatchProgress(batchId);
  };
};
