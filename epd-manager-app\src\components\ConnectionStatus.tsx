// EPD Manager App - 連接狀態指示器組件

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { useGateways } from '../stores/gatewayStore';
import { CONNECTION_STATUS, COLORS, SIZES } from '../utils/constants';
import { ConnectionStatus as ConnectionStatusType } from '../types';

interface ConnectionStatusProps {
  onPress?: () => void;
  showDetails?: boolean;
  style?: any;
}

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  onPress,
  showDetails = true,
  style
}) => {
  const { connectionStatus, selectedGateway, isConnected } = useGateways();

  const getStatusColor = (status: ConnectionStatusType): string => {
    switch (status) {
      case CONNECTION_STATUS.CONNECTED:
        return COLORS.CONNECTED;
      case CONNECTION_STATUS.CONNECTING:
        return COLORS.CONNECTING;
      case CONNECTION_STATUS.ERROR:
        return COLORS.CONNECTION_ERROR;
      case CONNECTION_STATUS.DISCONNECTED:
      default:
        return COLORS.DISCONNECTED;
    }
  };

  const getStatusText = (status: ConnectionStatusType): string => {
    switch (status) {
      case CONNECTION_STATUS.CONNECTED:
        return '已連接';
      case CONNECTION_STATUS.CONNECTING:
        return '連接中';
      case CONNECTION_STATUS.ERROR:
        return '連接錯誤';
      case CONNECTION_STATUS.DISCONNECTED:
      default:
        return '未連接';
    }
  };

  const getStatusIcon = (status: ConnectionStatusType): string => {
    switch (status) {
      case CONNECTION_STATUS.CONNECTED:
        return '🟢';
      case CONNECTION_STATUS.CONNECTING:
        return '🟡';
      case CONNECTION_STATUS.ERROR:
        return '🔴';
      case CONNECTION_STATUS.DISCONNECTED:
      default:
        return '⚪';
    }
  };

  const statusColor = getStatusColor(connectionStatus);
  const statusText = getStatusText(connectionStatus);
  const statusIcon = getStatusIcon(connectionStatus);

  return onPress ? (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.statusRow}>
        <View style={styles.statusIndicator}>
          <Text style={styles.statusIcon}>{statusIcon}</Text>
          <View style={[styles.statusDot, { backgroundColor: statusColor }]} />
        </View>
        
        <View style={styles.statusInfo}>
          <Text style={[styles.statusText, { color: statusColor }]}>
            {statusText}
          </Text>
          
          {showDetails && selectedGateway && (
            <Text style={styles.gatewayName}>
              {selectedGateway.name}
            </Text>
          )}
        </View>
      </View>

      {showDetails && isConnected && selectedGateway && (
        <View style={styles.detailsContainer}>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>MAC:</Text>
            <Text style={styles.detailValue}>{selectedGateway.macAddress}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>IP:</Text>
            <Text style={styles.detailValue}>{selectedGateway.ipAddress}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>型號:</Text>
            <Text style={styles.detailValue}>{selectedGateway.model}</Text>
          </View>
        </View>
      )}

      {showDetails && connectionStatus === CONNECTION_STATUS.DISCONNECTED && (
        <Text style={styles.hintText}>
          點擊「一鍵配對」開始連接網關
        </Text>
      )}

      {showDetails && connectionStatus === CONNECTION_STATUS.ERROR && (
        <Text style={styles.errorText}>
          連接失敗，請檢查網絡設置或重試
        </Text>
      )}
    </TouchableOpacity>
  ) : (
    <View style={[styles.container, style]}>
      <View style={styles.statusRow}>
        <View style={styles.statusIndicator}>
          <Text style={styles.statusIcon}>{statusIcon}</Text>
          <View style={[styles.statusDot, { backgroundColor: statusColor }]} />
        </View>

        <View style={styles.statusInfo}>
          <Text style={[styles.statusText, { color: statusColor }]}>
            {statusText}
          </Text>

          {showDetails && selectedGateway && (
            <Text style={styles.gatewayName}>
              {selectedGateway.name}
            </Text>
          )}
        </View>
      </View>

      {showDetails && isConnected && selectedGateway && (
        <View style={styles.detailsContainer}>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>MAC:</Text>
            <Text style={styles.detailValue}>{selectedGateway.macAddress}</Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>IP:</Text>
            <Text style={styles.detailValue}>{selectedGateway.ipAddress}</Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>型號:</Text>
            <Text style={styles.detailValue}>{selectedGateway.model}</Text>
          </View>
        </View>
      )}

      {showDetails && connectionStatus === CONNECTION_STATUS.DISCONNECTED && (
        <Text style={styles.hintText}>
          點擊「一鍵配對」開始連接網關
        </Text>
      )}

      {showDetails && connectionStatus === CONNECTION_STATUS.ERROR && (
        <Text style={styles.errorText}>
          連接失敗，請檢查網絡設置或重試
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    padding: SIZES.SPACING_MD,
    marginVertical: SIZES.SPACING_SM,
    borderWidth: 1,
    borderColor: COLORS.TEXT_DISABLED,
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: SIZES.SPACING_MD,
  },
  statusIcon: {
    fontSize: SIZES.FONT_SIZE_LG,
    marginRight: SIZES.SPACING_XS,
  },
  statusDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginLeft: SIZES.SPACING_XS,
  },
  statusInfo: {
    flex: 1,
  },
  statusText: {
    fontSize: SIZES.FONT_SIZE_MD,
    fontWeight: '600',
  },
  gatewayName: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
    marginTop: 2,
  },
  detailsContainer: {
    marginTop: SIZES.SPACING_MD,
    paddingTop: SIZES.SPACING_MD,
    borderTopWidth: 1,
    borderTopColor: COLORS.TEXT_DISABLED,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SIZES.SPACING_XS,
  },
  detailLabel: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
    fontWeight: '500',
  },
  detailValue: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_PRIMARY,
    fontFamily: 'monospace',
  },
  hintText: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    marginTop: SIZES.SPACING_SM,
    fontStyle: 'italic',
  },
  errorText: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.ERROR,
    textAlign: 'center',
    marginTop: SIZES.SPACING_SM,
  },
});

export default ConnectionStatus;
