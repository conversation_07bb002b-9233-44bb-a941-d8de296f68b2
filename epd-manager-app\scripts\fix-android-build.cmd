@echo off
echo EPD Manager App 修復工具
echo ===============================
cd /d "d:\CODE\git\epd-manager-lite\epd-manager-app"

echo 正在修復依賴項...
call npm install

echo 清理 Expo 緩存...
call npx expo-cli clear-cache

echo 清理 Android 構建緩存...
if exist "android\app\build" (
  echo 刪除 Android 構建文件...
  rmdir /s /q "android\app\build"
)

echo 重新生成 Android 構建文件...
call npx expo prebuild --platform android --clean

echo 更新 Gradle 配置...
if exist "android\gradle.properties" (
  echo org.gradle.jvmargs=-Xmx4608m -Dfile.encoding=UTF-8 >> android\gradle.properties
  echo android.useAndroidX=true >> android\gradle.properties
  echo android.enableJetifier=true >> android\gradle.properties
)

echo 構建應用...
cd android
call gradlew clean
call gradlew assembleDebug

echo 檢查構建結果...
if exist "app\build\outputs\apk\debug\app-debug.apk" (
  echo 構建成功！APK 文件位於：
  echo app\build\outputs\apk\debug\app-debug.apk
  
  echo 是否要安裝到設備上？（Y/N）
  set /p install=
  if /i "%install%"=="Y" (
    echo 安裝到設備上...
    cd ..
    call npx expo run:android --variant debug
  )
) else (
  echo 構建失敗，請檢查錯誤信息。
)

echo.
echo 修復完成。如果問題仍然存在，請運行診斷腳本：
echo scripts\diagnose-android-build.cmd

pause
