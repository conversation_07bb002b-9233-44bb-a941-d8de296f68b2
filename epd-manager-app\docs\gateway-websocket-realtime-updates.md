# EPD Manager App - 網關管理 WebSocket 即時更新實現

## 概述

為 EPD Manager App 的網關管理功能實現了 WebSocket 即時更新，現在網關狀態變化會自動即時反映在應用中，無需手動刷新。

## 實現內容

### 1. WebSocket 客戶端擴展

在 `services/WebSocketService.ts` 中擴展了 `FrontendWebSocketClient` 類：

#### 新增功能
- **網關狀態事件處理器**：`GatewayStatusEventHandler`
- **網關狀態訂閱**：`subscribeGatewayStatus()` / `unsubscribeGatewayStatus()`
- **網關狀態監聽器管理**：`addGatewayStatusListener()` / `removeGatewayStatusListener()`
- **網關狀態更新處理**：`handleGatewayStatusUpdate()`

#### 支持的消息類型
- `gateway_status_subscription_ack`: 網關狀態訂閱確認
- `gateway_status_update`: 網關狀態更新事件

### 2. 網關狀態管理擴展

在 `stores/gatewayStore.ts` 中新增了 WebSocket 相關功能：

#### 新增方法
```typescript
interface GatewayStore {
  // WebSocket 相關
  connectFrontendWebSocket: () => Promise<boolean>;
  disconnectFrontendWebSocket: () => void;
  subscribeToRealTimeUpdates: (storeId: string) => () => void;
  isFrontendWebSocketConnected: () => boolean;
}
```

#### 即時更新邏輯
- 自動更新現有網關狀態
- 支持新增網關的即時顯示
- 保持數據格式一致性（日期轉換等）

### 3. 網關管理頁面整合

在 `screens/GatewayManagementScreen.tsx` 中實現了完整的即時更新功能：

#### 自動化流程
1. **頁面加載時**：
   - 獲取網關列表
   - 連接前端 WebSocket
   - 訂閱當前門店的網關狀態更新

2. **即時更新**：
   - 接收網關狀態變化事件
   - 自動更新網關列表
   - 無需手動刷新

3. **錯誤處理**：
   - WebSocket 連接失敗時的降級處理
   - 自動重連機制

## 技術細節

### WebSocket 訂閱流程

```typescript
// 1. 連接前端 WebSocket
const connected = await connectFrontendWebSocket();

// 2. 訂閱網關狀態更新
const unsubscribe = subscribeToRealTimeUpdates(storeId);

// 3. 處理更新事件
const handleGatewayUpdate = (event) => {
  // 更新網關列表
  event.gateways.forEach(updatedGateway => {
    // 更新現有網關或添加新網關
  });
};
```

### 服務器端支持

利用現有的服務器端 WebSocket API：
- `subscribe_gateway_status`: 訂閱網關狀態
- `gateway_status_update`: 接收網關狀態更新
- 支持門店級別的訂閱過濾

### 數據同步

```typescript
// 網關狀態更新處理
event.gateways.forEach((updatedGateway: Gateway) => {
  const index = updatedGateways.findIndex(g => g._id === updatedGateway._id);
  if (index >= 0) {
    // 更新現有網關
    updatedGateways[index] = {
      ...updatedGateways[index],
      ...updatedGateway,
      lastSeen: new Date(updatedGateway.lastSeen),
      updatedAt: new Date(updatedGateway.updatedAt),
    };
  } else if (event.updateType === 'create') {
    // 添加新網關
    updatedGateways.push(processedGateway);
  }
});
```

## 使用方式

### 自動即時更新

現在網關管理頁面會：
1. **自動連接**：頁面加載時自動連接 WebSocket
2. **即時訂閱**：訂閱當前門店的網關狀態更新
3. **即時更新**：網關狀態變化時自動更新列表
4. **智能降級**：WebSocket 連接失敗時保持原有功能

### 支持的更新類型

- **狀態變化**：在線/離線狀態即時更新
- **連接信息**：WebSocket 連接狀態更新
- **固件信息**：固件版本更新
- **網關信息**：名稱、IP 地址等基本信息更新
- **新增網關**：新網關自動出現在列表中

## 配置說明

### WebSocket 地址配置

網關管理使用與設備管理相同的 WebSocket 配置：
- 自動使用用戶在登入頁面設定的服務器 IP
- 端口固定為 3001
- 協議為 WebSocket (ws://)

### 訂閱選項

```typescript
subscribeToGatewayStatus(storeId, handler, {
  includeConnectionInfo: true,  // 包含連接信息
  includeFirmwareInfo: true     // 包含固件信息
});
```

## 性能優化

### 防抖機制
- 服務器端使用防抖機制避免頻繁更新
- 批量處理多個網關的狀態變化

### 選擇性更新
- 只更新實際變化的網關
- 保持未變化網關的狀態不變

### 內存管理
- 自動清理訂閱和監聽器
- 組件卸載時正確清理資源

## 測試驗證

### 驗證步驟
1. 啟動 app 並進入網關管理頁面
2. 檢查控制台日誌，確認 WebSocket 連接成功
3. 在 web 端或其他客戶端修改網關狀態
4. 觀察 app 是否自動更新，無需手動刷新

### 預期日誌
```
連接前端 WebSocket（網關管理）...
已訂閱門店 store123 的即時網關更新
收到網關狀態更新事件: {...}
已更新 2 個網關的狀態
```

## 故障排除

如果即時更新不工作：

1. **檢查 WebSocket 連接**：
   - 確認服務器地址配置正確
   - 檢查網絡連接和防火牆設置

2. **檢查訂閱狀態**：
   - 查看控制台日誌中的訂閱確認消息
   - 確認門店 ID 正確

3. **檢查服務器端**：
   - 確認服務器 WebSocket 服務正常運行
   - 檢查服務器端的網關狀態廣播功能

## 與設備管理的一致性

網關管理的 WebSocket 實現與設備管理保持一致：
- 使用相同的前端 WebSocket 客戶端
- 相同的連接管理和錯誤處理機制
- 統一的配置管理和地址獲取方式

現在 EPD Manager App 的網關管理功能具備了完整的即時更新能力！🎉
