# EPD Manager App 實現計劃

## 1. 專案概述

本文檔提供了 EPD Manager App 的開發計劃和設計細節，旨在創建一個自動化橋樑應用，消除 SERVER 和 GATEWAY 之間手動配對的繁瑣流程。

### 1.1 背景介紹

EPD Manager 是一個電子紙顯示器管理系統，通過雲平台能實現對系統內所有網關、電子紙顯示器的控制。

**當前問題：**
- 網關模擬器（`ws-client-from-copied-info.js`）生成 MAC 地址後，需要手動複製到 Web 端新增網關
- Web 端生成 WebSocket 登入資訊後，需要手動複製到網關模擬器
- 整個配對流程需要在兩個系統間來回複製，容易出錯且效率低下

**APP 解決方案：**
EPD Manager App 作為 SERVER 和 GATEWAY 之間的自動化橋樑，用戶只需在 APP 中登入帳號密碼，即可自動完成所有新增與配對流程，行為類似 `test-ws-client-interactive.js` 的自動化版本。

### 1.2 目標用戶

- 系統管理員（需要快速配對新網關）
- 門店管理人員（現場設備部署）
- 設備維護人員（設備故障排除和更換）
- 技術人員（系統測試和調試）

### 1.3 應用名稱

應用名稱為 "EPD Manager"，在各大應用商店中以此名稱發布。

## 2. 技術架構

### 2.1 開發框架選擇

#### 移動端開發框架
- **React Native**: 選擇 React Native 作為主要開發框架，實現跨平台(iOS 和 Android)開發
- **Expo**: 使用 Expo 工具加速開發流程，簡化配置和部署

#### 後端服務
- 使用現有的 EPD Manager 後端服務 (Express.js + MongoDB)
- 通過 RESTful API 和 WebSocket 與後端通信
- 支援 JWT 認證和權限管理系統

#### 數據存儲
- **AsyncStorage**: 用於本地數據緩存
- **Zustand**: 用於應用狀態管理（與主專案保持一致）
- **React Query**: 用於 API 數據緩存和同步
- **Redux Persist**: 實現持久化存儲（如需要）

### 2.2 系統架構圖

```mermaid
graph TB
    subgraph "移動應用架構"
        App["EPD Manager App\nReact Native + Expo"]
        UI["UI 組件\nReact Native Elements"]
        State["狀態管理\nRedux + Redux Persist"]
        Storage["本地存儲\nAsyncStorage"]
        Network["網絡層\nAxios + WebSocket"]
        
        App --> UI
        App --> State
        State --> Storage
        App --> Network
    end
    
    subgraph "後端服務"
        Server["EPD Manager Server\nExpress.js"]
        DB["數據庫\nMongoDB"]
        WSService["WebSocket 服務"]
        
        Server <--> DB
        Server <--> WSService
    end
    
    Network <--> Server
    Network <--> WSService
    
    subgraph "外部設備"
        Gateway["網關設備"]
        EPD["電子紙顯示器"]
        
        Gateway <--> EPD
    end
    
    WSService <--> Gateway
```

### 2.3 技術依賴

#### 核心框架
- React Native v0.71+
- Expo SDK 49+
- TypeScript (類型安全)

#### 狀態管理與數據
- Zustand (狀態管理，與主專案一致)
- React Query / TanStack Query (API 數據管理)
- AsyncStorage (本地存儲)

#### 導航與 UI
- React Navigation v6+
- React Native Elements / NativeBase (UI 組件庫)
- React Native Vector Icons
- React Native Reanimated (動畫)

#### 網絡與通信
- Axios (HTTP 請求)
- React Native WebSocket (WebSocket 通信)
- React Native NetInfo (網絡狀態檢測)

#### 設備功能
- React Native UDP (網關掃描)
- React Native Camera (二維碼掃描)
- React Native Permissions (權限管理)
- React Native File System (文件操作)

#### 開發工具
- ESLint + Prettier (代碼規範)
- Jest + React Native Testing Library (測試)
- Flipper (調試工具)

## 3. 功能規劃

### 3.1 核心功能列表

基於自動化橋樑的核心目標，APP 需要實現以下功能：

#### **主要功能：自動化配對流程**

1. **一鍵網關配對**
   - 用戶登入後選擇門店
   - 自動生成網關 MAC 地址
   - 自動調用 API 創建網關
   - 自動獲取 WebSocket 連接資訊
   - 自動建立 WebSocket 連接
   - 模擬網關行為（類似 `test-ws-client-interactive.js`）

2. **網關模擬功能**
   - 自動發送心跳消息（ping/pong）
   - 自動發送網關信息消息
   - 自動發送設備狀態消息
   - 處理服務器命令和回應
   - 模擬設備添加/移除

#### **支援功能**

3. **用戶認證**
   - JWT 登入/登出功能
   - 記住登入狀態
   - 自動 Token 刷新

4. **門店管理**
   - 門店列表查看
   - 門店選擇（配對前必須選擇門店）

5. **網關管理**
   - 查看已配對的網關列表
   - 網關連接狀態監控
   - 網關詳情查看
   - 斷開/重新連接網關

6. **設備模擬管理**
   - 添加模擬設備（類似 `test-ws-client-interactive.js` 的 add 命令）
   - 查看模擬設備列表（類似 list 命令）
   - 移除模擬設備（類似 remove 命令）
   - 請求設備預覽圖像（類似 request-image 命令）

7. **WebSocket 通信監控**
   - 連接狀態實時顯示
   - 消息收發日誌
   - 錯誤處理和重連機制

8. **系統功能**
   - 設置管理（服務器地址配置）
   - 日誌查看和導出
   - 幫助和說明

### 3.2 自動化配對流程圖

```mermaid
sequenceDiagram
    participant User as 用戶
    participant App as EPD Manager App
    participant Server as 後端服務器

    Note over User,Server: 自動化配對流程（消除手動複製）

    User->>App: 登入帳號密碼
    App->>Server: 發送登入請求
    Server-->>App: 返回認證 Token

    User->>App: 選擇門店
    App->>Server: 獲取門店列表
    Server-->>App: 返回門店列表

    User->>App: 點擊「一鍵配對新網關」

    Note over App: 自動化流程開始
    App->>App: 生成隨機 MAC 地址
    App->>App: 生成網關名稱和配置

    App->>Server: 自動創建新網關
    Note right of App: 包含生成的 MAC 地址
    Server-->>App: 返回網關信息和 WebSocket 配置
    Note right of Server: 包含 token, url, path 等

    App->>App: 解析 WebSocket 配置
    App->>Server: 自動建立 WebSocket 連接
    Note right of App: 使用返回的 token 和 url
    Server-->>App: 連接確認（welcome 消息）

    Note over App,Server: 開始模擬網關行為

    loop 自動運行
        App->>Server: 發送心跳消息 (ping)
        Server-->>App: 回應 (pong)
        App->>Server: 發送網關信息
        App->>Server: 發送設備狀態
        Server-->>App: 處理並回應
    end

    Note over User,Server: 用戶可以進行設備管理

    User->>App: 添加模擬設備
    App->>App: 更新本地設備列表
    App->>Server: 發送更新的設備狀態

    User->>App: 請求設備預覽圖像
    App->>Server: 發送圖像請求
    Server-->>App: 返回圖像數據
    App->>App: 顯示並保存圖像
```

## 4. 用戶界面設計

### 4.1 主要頁面

基於自動化配對的核心功能，重新設計頁面結構：

1. **登入頁面**
   - 用戶名/密碼輸入
   - 伺服器地址配置
   - 記住我選項
   - 快速連接按鈕

2. **門店選擇頁面**
   - 門店列表（卡片式顯示）
   - 搜索功能
   - 門店狀態指示

3. **主控制台頁面**（核心頁面）
   - **一鍵配對按鈕**（主要功能）
   - 當前連接狀態顯示
   - 已配對網關列表
   - 快速操作按鈕

4. **網關詳情頁面**
   - 網關基本信息
   - WebSocket 連接狀態
   - 連接/斷開按鈕
   - 刪除網關選項

5. **設備模擬管理頁面**
   - 模擬設備列表
   - 添加設備按鈕（類似 test-ws-client-interactive.js 的 add）
   - 設備操作選項（移除、請求圖像）
   - 設備狀態實時更新

6. **WebSocket 監控頁面**
   - 連接狀態指示燈
   - 消息收發日誌
   - 心跳狀態顯示
   - 錯誤日誌

7. **設置頁面**
   - 服務器配置
   - 自動重連設置
   - 日誌級別設置
   - 關於和幫助

### 4.2 導航結構

```mermaid
graph TD
    Login[登入頁面] --> StoreSelect[門店選擇頁面]
    StoreSelect --> MainConsole[主控制台頁面]

    MainConsole --> |一鍵配對| AutoPairing[自動配對流程]
    MainConsole --> |查看網關| GatewayList[網關列表]
    MainConsole --> |設備管理| DeviceSimulation[設備模擬管理]
    MainConsole --> |監控| WSMonitor[WebSocket 監控]
    MainConsole --> |設置| Settings[設置頁面]

    GatewayList --> GatewayDetail[網關詳情]
    GatewayDetail --> |連接/斷開| WSConnection[WebSocket 連接]

    DeviceSimulation --> AddDevice[添加模擬設備]
    DeviceSimulation --> DeviceActions[設備操作]
    DeviceActions --> RequestImage[請求圖像]

    WSMonitor --> MessageLog[消息日誌]
    WSMonitor --> ConnectionStatus[連接狀態]

    AutoPairing --> |成功| MainConsole
    AutoPairing --> |失敗| ErrorHandling[錯誤處理]
```

## 5. 技術實現細節

### 5.1 用戶認證

使用 JWT 認證機制，與後端服務保持一致：

```javascript
// 登入函數
async function login(username, password) {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ username, password })
    });

    if (!response.ok) {
      throw new Error(`登入失敗: ${response.status}`);
    }

    const data = await response.json();
    return data.token;
  } catch (error) {
    console.error('登入錯誤:', error);
    throw error;
  }
}
```

### 5.2 WebSocket 連接

實現與後端的 WebSocket 連接：

```javascript
// 建立 WebSocket 連接
function connectWebSocket(gateway, storeId, token) {
  // 構建 WebSocket URL
  const url = `ws://${API_HOST}:${API_PORT}/ws/store/${storeId}/gateway/${gateway._id}?token=${token}`;
  
  const ws = new WebSocket(url);
  
  ws.onopen = () => {
    console.log('WebSocket 連接已建立');
    
    // 發送 ping 消息
    sendPingMessage(ws);
    
    // 設置定期發送心跳
    const pingInterval = setInterval(() => {
      sendPingMessage(ws);
    }, 25000);
    
    // 設置定期發送設備狀態
    const deviceStatusInterval = setInterval(() => {
      sendDeviceStatusMessage(ws);
    }, 5000);
    
    // 設置發送網關信息
    setTimeout(() => {
      sendGatewayInfoMessage(ws, gateway);
    }, 30000);
    
    return {
      ws,
      intervals: {
        ping: pingInterval,
        deviceStatus: deviceStatusInterval
      }
    };
  };
  
  ws.onmessage = (event) => {
    handleWebSocketMessage(event.data);
  };
  
  ws.onerror = (error) => {
    console.error('WebSocket 錯誤:', error);
  };
  
  ws.onclose = (event) => {
    console.log(`WebSocket 連接已關閉: ${event.code} ${event.reason}`);
    // 處理重連邏輯
  };
  
  return ws;
}
```

## 6. 開發計劃

### 6.1 開發階段

1. **準備階段** (1週)
   - 環境搭建
   - 項目初始化
   - 技術選型確認

2. **基礎功能開發** (2週)
   - 用戶認證
   - 門店管理
   - 網關管理基礎功能

3. **核心功能開發** (3週)
   - WebSocket 通信
   - 設備管理
   - 圖像處理

4. **UI 優化與測試** (2週)
   - 界面美化
   - 用戶體驗優化
   - 功能測試與修復

5. **發布準備** (1週)
   - 性能優化
   - 文檔完善
   - 打包與發布準備

### 6.2 里程碑

1. **M1**: 完成基礎框架搭建與用戶認證 (第1週結束)
2. **M2**: 完成門店與網關管理功能 (第3週結束)
3. **M3**: 完成 WebSocket 通信與設備管理 (第6週結束)
4. **M4**: 完成所有功能與 UI 優化 (第8週結束)
5. **M5**: 應用發布準備就緒 (第9週結束)

## 7. 總結

EPD Manager App 將為用戶提供便捷的移動端管理體驗，使其能夠隨時隨地管理電子紙顯示系統。通過本開發計劃的實施，我們將在9週內完成一個功能完整、用戶體驗良好的移動應用，滿足用戶的各種管理需求。
