// EPD Manager App - 圖標生成腳本

const fs = require('fs');
const path = require('path');

// 創建 SVG 圖標
const createSVGIcon = (size, backgroundColor = '#1976D2', textColor = '#FFFFFF') => {
  return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <rect width="${size}" height="${size}" fill="${backgroundColor}" rx="${size * 0.1}"/>
  <text x="50%" y="35%" text-anchor="middle" font-family="Arial, sans-serif" font-size="${size * 0.25}" font-weight="bold" fill="${textColor}">EPD</text>
  <text x="50%" y="65%" text-anchor="middle" font-family="Arial, sans-serif" font-size="${size * 0.15}" font-weight="normal" fill="${textColor}">Manager</text>
  <circle cx="${size * 0.8}" cy="${size * 0.2}" r="${size * 0.05}" fill="#4CAF50"/>
</svg>`;
};

// 創建啟動畫面 SVG
const createSplashSVG = (width = 1242, height = 2688) => {
  return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">
  <rect width="${width}" height="${height}" fill="#1976D2"/>
  <g transform="translate(${width/2}, ${height/2})">
    <rect x="-150" y="-150" width="300" height="300" fill="#FFFFFF" rx="30"/>
    <text x="0" y="-30" text-anchor="middle" font-family="Arial, sans-serif" font-size="60" font-weight="bold" fill="#1976D2">EPD</text>
    <text x="0" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="36" font-weight="normal" fill="#1976D2">Manager</text>
    <text x="0" y="80" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" fill="#666666">自動化橋樑應用</text>
    <circle cx="100" cy="-100" r="12" fill="#4CAF50"/>
  </g>
</svg>`;
};

// 確保 assets 目錄存在
const assetsDir = path.join(__dirname, '..', 'assets');
if (!fs.existsSync(assetsDir)) {
  fs.mkdirSync(assetsDir, { recursive: true });
}

try {
  // 生成應用圖標 (1024x1024)
  const iconSVG = createSVGIcon(1024);
  fs.writeFileSync(path.join(assetsDir, 'icon.svg'), iconSVG);
  console.log('✅ 生成 icon.svg');

  // 生成自適應圖標 (1024x1024)
  const adaptiveIconSVG = createSVGIcon(1024, '#1976D2', '#FFFFFF');
  fs.writeFileSync(path.join(assetsDir, 'adaptive-icon.svg'), adaptiveIconSVG);
  console.log('✅ 生成 adaptive-icon.svg');

  // 生成啟動畫面
  const splashSVG = createSplashSVG();
  fs.writeFileSync(path.join(assetsDir, 'splash.svg'), splashSVG);
  console.log('✅ 生成 splash.svg');

  // 生成 favicon (32x32)
  const faviconSVG = createSVGIcon(32);
  fs.writeFileSync(path.join(assetsDir, 'favicon.svg'), faviconSVG);
  console.log('✅ 生成 favicon.svg');

  // 創建 PNG 版本的說明文件
  const readmeContent = `# EPD Manager App Assets

## 圖標文件說明

本目錄包含 EPD Manager App 的所有圖標和啟動畫面資源。

### 文件列表

- \`icon.svg\` - 主應用圖標 (1024x1024)
- \`adaptive-icon.svg\` - Android 自適應圖標 (1024x1024)
- \`splash.svg\` - 啟動畫面 (1242x2688)
- \`favicon.svg\` - Web 版本圖標 (32x32)

### 轉換為 PNG

如需要 PNG 格式的圖標，可以使用以下工具：

1. **在線轉換工具**：
   - https://convertio.co/svg-png/
   - https://cloudconvert.com/svg-to-png

2. **命令行工具**：
   \`\`\`bash
   # 使用 ImageMagick
   magick icon.svg icon.png
   
   # 使用 Inkscape
   inkscape icon.svg --export-png=icon.png
   \`\`\`

3. **建議的 PNG 尺寸**：
   - icon.png: 1024x1024
   - adaptive-icon.png: 1024x1024
   - splash.png: 1242x2688
   - favicon.png: 32x32

### 設計說明

- **主色調**：#1976D2 (Material Design Blue)
- **文字顏色**：#FFFFFF (白色)
- **狀態指示**：#4CAF50 (綠色圓點，表示在線狀態)
- **設計風格**：簡潔現代，符合 Material Design 規範

### 使用方法

1. 將 SVG 文件轉換為對應的 PNG 格式
2. 確保文件名與 app.json 中的配置一致
3. 重新構建應用以應用新圖標
`;

  fs.writeFileSync(path.join(assetsDir, 'README.md'), readmeContent);
  console.log('✅ 生成 README.md');

  console.log('\n🎉 所有圖標文件生成完成！');
  console.log('\n📝 下一步：');
  console.log('1. 將 SVG 文件轉換為 PNG 格式');
  console.log('2. 確保文件名與 app.json 配置一致');
  console.log('3. 運行 expo build 或 eas build');

} catch (error) {
  console.error('❌ 生成圖標時發生錯誤:', error);
  process.exit(1);
}
