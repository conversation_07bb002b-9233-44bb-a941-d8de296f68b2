// 測試過濾邏輯
import { Gateway } from '../../types';

// 模擬網關數據
const mockGateways: Gateway[] = [
  {
    _id: '1',
    name: 'Gateway 1',
    macAddress: 'AA:BB:CC:DD:EE:01',
    status: 'online',
    model: 'GW-1000',
    wifiFirmwareVersion: '1.0.0',
    btFirmwareVersion: '2.0.0',
    ipAddress: '*************',
    storeId: 'store1',
    lastSeen: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    _id: '2',
    name: 'Gateway 2',
    macAddress: 'AA:BB:CC:DD:EE:02',
    status: 'offline',
    model: 'GW-2000',
    wifiFirmwareVersion: '1.1.0',
    btFirmwareVersion: '2.1.0',
    ipAddress: '*************',
    storeId: 'store1',
    lastSeen: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    _id: '3',
    name: 'Gateway 3',
    macAddress: 'AA:BB:CC:DD:EE:03',
    status: 'online',
    model: 'GW-1000',
    wifiFirmwareVersion: '1.0.0',
    btFirmwareVersion: '2.0.0',
    ipAddress: '*************',
    storeId: 'store1',
    lastSeen: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

describe('Gateway Filter Logic', () => {
  it('should filter online gateways correctly', () => {
    const onlineGateways = mockGateways.filter(gateway => gateway.status === 'online');
    expect(onlineGateways).toHaveLength(2);
    expect(onlineGateways.every(g => g.status === 'online')).toBe(true);
  });

  it('should filter offline gateways correctly', () => {
    const offlineGateways = mockGateways.filter(gateway => gateway.status === 'offline');
    expect(offlineGateways).toHaveLength(1);
    expect(offlineGateways.every(g => g.status === 'offline')).toBe(true);
  });

  it('should return all gateways when no filter is applied', () => {
    const allGateways = mockGateways;
    expect(allGateways).toHaveLength(3);
  });

  it('should calculate filter counts correctly', () => {
    const onlineCount = mockGateways.filter(g => g.status === 'online').length;
    const offlineCount = mockGateways.filter(g => g.status === 'offline').length;
    
    expect(onlineCount).toBe(2);
    expect(offlineCount).toBe(1);
    expect(onlineCount + offlineCount).toBe(mockGateways.length);
  });
});
