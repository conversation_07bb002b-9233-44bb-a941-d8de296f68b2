// 模板類型枚舉
export enum TemplateType {
  SINGLE_DATA = "Single data template",
  MULTIPLE_DATA = "Multiple data template"
}

export interface Template {
  id: string;
  name: string;
  type: TemplateType;
  screenSize: string;
  color: string;
  orientation: string;
  elements: TemplateElement[];
  previewImage?: string; // 添加預覽圖字段（base64格式）
  storeId?: string;      // 關聯的門店ID，如果為空則表示可用於所有門店
  isSystemTemplate?: boolean; // 是否為系統模板，系統模板可被所有門店使用
}

export interface TemplateElement {
  id: string;
  type: 'text' | 'multiline-text' | 'image' | 'qr-code' | 'barcode' | 'icon' | 'rectangle' | 'square' | 'line' | 'circle' | 'ellipse';
  x: number;
  y: number;
  width: number;
  height: number;
  rotation?: number;
  content?: string;
  fontSize?: number;
  fontFamily?: string;
  lineWidth?: number;
  lineColor?: string;
  fillColor?: string;
  iconType?: string;
  imageUrl?: string; // 圖片 URL 屬性
  endX?: number;     // 新增：線條結束 X 座標
  endY?: number;     // 新增：線條結束 Y 座標
  dataFieldId?: string | null; // 已棄用：保留此屬性以向後兼容
  dataBinding?: BindingInfo; // 新增：資料綁定信息
  templateStoreId?: string; // 新增：元素所屬模板的門店ID

  // QR Code 和條碼專用屬性
  qrCodeType?: 'qrcode' | 'datamatrix' | 'pdf417';  // QR Code 類型
  barcodeType?: 'code128' | 'ean13' | 'upc-a' | 'code39' | 'code93';  // 條碼類型
  codeContent?: string;  // 編碼內容（靜態內容或預設值）
  errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H';  // QR Code 錯誤修正等級
  quietZone?: number;  // 靜區大小
  moduleSize?: number;  // 模組大小（影響密度）
  showBorder?: boolean;  // 是否顯示邊框
  borderColor?: string;  // 邊框顏色
  showText?: boolean;  // 條碼是否顯示文字（預設 false）

  // 預覽圖片儲存
  previewImageUrl?: string;  // 後端生成的預覽圖片 URL 或 base64 數據
}

// 顯示顏色類型
export enum DisplayColorType {
  BW = "Gray16",
  BWR = "Black & White & Red",
  BWRY = "Black & White & Red & Yellow"
}

// 將字符串轉換為對應的 DisplayColorType
export const stringToDisplayColorType = (color: string): DisplayColorType | undefined => {
  switch (color) {
    case "Gray16": return DisplayColorType.BW;
    case "Black & White & Red": return DisplayColorType.BWR;
    case "Black & White & Red & Yellow": return DisplayColorType.BWRY;
    default: return undefined;
  }
};

// 將簡短的顏色類型代碼轉換為 DisplayColorType 值
export const shortCodeToDisplayColorType = (code: string): string => {
  if (!code) return "UNKNOWN";

  switch (code.toUpperCase()) {
    case 'BW':
      return DisplayColorType.BW;
    case 'BWR':
      return DisplayColorType.BWR;
    case 'BWRY':
      return DisplayColorType.BWRY;
    default:
      console.warn(`未知的顏色類型代碼: ${code}，將顯示為 UNKNOWN`);
      return "UNKNOWN";
  }
};

// 獲取所有顯示顏色類型的值
export const getAllDisplayColorTypes = (): string[] => {
  return Object.values(DisplayColorType);
};

// 資料欄位類型枚舉
export enum DataFieldType {
  UNIQUE_IDENTIFIER = "unique identifier",
  TEXT = "pure text",
  NUMBER = "number",
  BARCODE_CODE128 = "barcode-code128",
  BARCODE_EAN13 = "barcode-ean13",
  BARCODE_UPC_A = "barcode-upc-a",
  BARCODE_CODE39 = "barcode-code39",
  BARCODE_CODE93 = "barcode-code93",
  QR_CODE = "QR code-qrcode",
  QR_CODE_DATAMATRIX = "QR code-datamatrix",
  QR_CODE_PDF417 = "QR code-pdf417"
}

// 資料欄位區塊類型枚舉
export enum DataFieldSectionType {
  ORDINARY = "ordinary",
  ICON = "icon",
  IMAGE = "image",
  VIDEO = "video"
}

// 資料欄位接口
export interface DataField {
  id: string;
  type: string;
  name: string;
  prefix: string;
  section: DataFieldSectionType;
  sortOrder?: number; // 用於排序
  description?: string;
  defaultValue?: string;
}

// 資料欄位同步狀態接口
export interface DataFieldSyncStatus {
  lastSynced: Date | null;
  isModified: boolean;
}

// 資料綁定相關介面
export interface BindingInfo {
  dataIndex: number;           // 綁定的數據索引 (0 到 maxBindingDataCount-1)
  fieldId: string | null;      // 綁定的資料欄位ID
  selectedStoreId?: string | null; // 預覽門店數據的選擇門店ID
  displayOptions?: {           // 元件特定的顯示選項
    showPrefix?: boolean;      // 是否顯示前綴
  }
}

// 導入新的 StoreSpecificData 接口
import { StoreSpecificData } from './types/store';

// 門店資料基本接口 - 直接使用 StoreSpecificData
export type StoreData = StoreSpecificData;

// 門店資料狀態接口
export interface StoreDataStatus {
  lastSynced: Date | null;
  isLoading: boolean;
  error: string | null;
}

// 裝置綁定關係
export interface DeviceBinding {
  deviceId: string;           // 裝置ID
  templateId: string;         // 模板ID
  dataIds: Record<number, string>;  // 數據索引 -> 實際數據ID的映射
}