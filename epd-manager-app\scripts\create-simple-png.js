const fs = require('fs');
const path = require('path');

// Create a simple 1x1 pixel PNG (base64 encoded)
const createSimplePNG = (filename) => {
  // This is a 1x1 blue pixel PNG in base64
  const base64PNG = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==';
  const buffer = Buffer.from(base64PNG, 'base64');
  
  const filePath = path.join(__dirname, '..', 'assets', filename);
  fs.writeFileSync(filePath, buffer);
  console.log(`Created ${filename}`);
};

// Create basic PNG files
createSimplePNG('icon.png');
createSimplePNG('adaptive-icon.png');
createSimplePNG('splash.png');
createSimplePNG('favicon.png');

console.log('All PNG icons created successfully!');
