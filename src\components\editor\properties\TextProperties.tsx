import React, { useEffect, useState } from 'react';
import { TemplateElement, DataField, DisplayColorType } from '../../../types';
import { Store } from '../../../types/store';
import { FormField, TextInput, NumberInput, SelectInput, fontOptions, RestrictedColorInput } from './FormComponents';
import { getAllDataFields } from '../../../utils/api/dataFieldApi';
import { getAllStores } from '../../../utils/api/storeApi';
import { bindingCore } from '../../../utils/dataBinding/bindingCore';
import { TextBinding } from '../../../utils/dataBinding/textBinding';

interface TextPropertiesProps {
  element: TemplateElement;
  updateElement: (updates: Partial<TemplateElement>) => void;
  colorType?: string | DisplayColorType; // 新增：模板的顏色類型
}

export const TextProperties: React.FC<TextPropertiesProps> = ({ element, updateElement, colorType }) => {
  // 判斷是否為多行文字元素
  const isMultilineText = element.type === 'multiline-text';

  // 添加資料欄位狀態和加載功能
  const [dataFields, setDataFields] = useState<DataField[]>([]);
  const [isLoadingDataFields, setIsLoadingDataFields] = useState(false);

  // 添加門店數據狀態
  const [storeData, setStoreData] = useState<Store[]>([]);
  const [selectedStoreId, setSelectedStoreId] = useState<string | null>(
    element.dataBinding?.selectedStoreId || null
  );

  // 添加樣本數據狀態
  const [previewValue, setPreviewValue] = useState<string | null>(null);

  // 兼容舊版 dataFieldId 和新版 dataBinding.fieldId
  const [selectedDataFieldId, setSelectedDataFieldId] = useState<string | null>(
    (element.dataBinding?.fieldId || element.dataFieldId || null)
  );

  // 資料綁定相關狀態
  const [dataIndex, setDataIndex] = useState<number>(
    element.dataBinding?.dataIndex || 0
  );
  const [showPrefix, setShowPrefix] = useState<boolean>(
    element.dataBinding?.displayOptions?.showPrefix || false
  );
  // 當前最大資料綁定數量
  const [maxBindingDataCount, setMaxBindingDataCount] = useState<number>(bindingCore.getMaxBindingDataCount());

  // 生成資料索引選項
  const dataIndexOptions = Array.from({ length: maxBindingDataCount }, (_, i) => ({
    value: String(i),
    label: `資料 ${i + 1}`
  }));

  // 監聽最大綁定數量的變更
  useEffect(() => {
    // 當最大綁定數量變更時更新狀態
    const handleMaxBindingDataCountChange = (count: number) => {
      setMaxBindingDataCount(count);
    };

    // 添加監聽器
    bindingCore.addMaxBindingDataCountListener(handleMaxBindingDataCountChange);

    // 清理函數：組件卸載時移除監聽器
    return () => {
      bindingCore.removeMaxBindingDataCountListener(handleMaxBindingDataCountChange);
    };
  }, []);
  // 在組件加載時獲取資料欄位，並檢查資料綁定
  useEffect(() => {
    const fetchDataFields = async () => {
      setIsLoadingDataFields(true);
      try {
        const fields = await getAllDataFields();
        // 使用 TextBinding 的過濾方法，只顯示文字元件可用的欄位類型
        const bindableFields = TextBinding.getBindableFields(fields);
        setDataFields(bindableFields);

        // 檢查資料綁定有效性
        checkDataBindingValidity(bindableFields);
      } catch (error) {
        console.error('獲取資料欄位失敗:', error);
      } finally {
        setIsLoadingDataFields(false);
      }
    };

    fetchDataFields();
  }, []);

  // 在組件首次加載時更新預覽值
  useEffect(() => {
    console.log('元素首次加載，元素ID:', element.id, '綁定狀態:', !!element.dataBinding);

    // 如果元素有綁定信息，立即更新預覽值
    if (element.dataBinding?.fieldId || element.dataFieldId) {
      console.log('元素有綁定信息，立即更新預覽值');

      // 不設置臨時值，等待真實數據加載完成後再設置
      // setPreviewValue('Text');

      // 設置綁定相關的狀態
      if (element.dataBinding?.fieldId) {
        setSelectedDataFieldId(element.dataBinding.fieldId);
        setDataIndex(element.dataBinding.dataIndex || 0);
        setShowPrefix(element.dataBinding.displayOptions?.showPrefix || false);

        // 如果元素有存儲的門店ID，則使用它
        if (element.dataBinding.selectedStoreId) {
          setSelectedStoreId(element.dataBinding.selectedStoreId);
        }
      } else if (element.dataFieldId) {
        setSelectedDataFieldId(element.dataFieldId);
        setDataIndex(0);
        setShowPrefix(false);
      }

      // 直接獲取所有資料欄位和門店數據
      Promise.all([
        getAllDataFields(),
        getAllStores()
      ]).then(([allFields, stores]) => {
        console.log('首次加載時獲取到資料欄位和門店數據，欄位數量:', allFields.length, '門店數量:', stores.length);
        // 輸出所有欄位的ID，以便確認是否存在"description"欄位
        console.log('所有欄位的ID:', allFields.map(field => field.id));
        // 輸出所有欄位的詳細信息
        console.log('所有欄位的詳細信息:', allFields);

        // 更新狀態
        // 使用 TextBinding 的過濾方法，只顯示文字元件可用的欄位類型
        const bindableFields = TextBinding.getBindableFields(allFields);
        setDataFields(bindableFields);
        setStoreData(stores);

        // 確保有門店ID
        if (!selectedStoreId && stores.length > 0) {
          if (element.dataBinding?.selectedStoreId) {
            setSelectedStoreId(element.dataBinding.selectedStoreId);
          } else if (stores[0].id) {
            console.log('自動設置門店ID為第一個門店:', stores[0].id);
            setSelectedStoreId(stores[0].id);
          }
        }

        // 查找欄位，不管它是否可綁定
        const fieldId = element.dataBinding?.fieldId || element.dataFieldId;
        const currentField = allFields.find(f => f.id === fieldId);
        if (!currentField) {
          console.log('在所有欄位中找不到欄位信息，欄位ID:', fieldId, '但仍嘗試從門店數據中獲取值');

          // 檢查門店數據中是否有這個欄位
          const storeId = element.dataBinding?.selectedStoreId || selectedStoreId;
          const currentStore = stores.find(store => store.id === storeId);

          if (currentStore && currentStore.storeSpecificData &&
              Array.isArray(currentStore.storeSpecificData) &&
              currentStore.storeSpecificData.length > 0) {

            // 檢查第一筆門店專屬數據是否有這個欄位
            const firstStoreData = currentStore.storeSpecificData[0];
            if (firstStoreData && fieldId && (firstStoreData as any)[fieldId] !== undefined) {
              console.log('在門店數據中找到欄位:', fieldId, '值:', (firstStoreData as any)[fieldId]);
              // 繼續處理，不要返回
            } else {
              console.log('在門店數據中也找不到欄位:', fieldId);
              // 即使找不到欄位，也繼續處理，不要返回
            }
          }
        }

        // 找到當前門店
        const storeId = element.dataBinding?.selectedStoreId || selectedStoreId;
        const currentStore = stores.find(store => store.id === storeId);
        if (!currentStore) {
          console.log('找不到選擇的門店，顯示找不到門店提示');
          setPreviewValue('Text');
          return;
        }

        // 延遲處理，確保狀態已更新
        setTimeout(() => {
          console.log('首次加載時更新預覽值');
          // 使用找到的欄位和門店處理數據
          updatePreviewValueWithCurrentStore();
        }, 300); // 增加延遲時間，確保狀態已更新
      }).catch(error => {
        console.error('首次加載時獲取數據失敗:', error);
        setPreviewValue('Text');
      });
    }
  }, []);

  // 在組件加載時獲取門店數據
  useEffect(() => {
    const fetchStoreData = async () => {
      if (!selectedDataFieldId) return;

      console.log('開始獲取門店數據，當前選擇的門店ID:', selectedStoreId);

      try {
        const stores = await getAllStores();
        console.log('獲取到門店數據，數量:', stores.length);
        // 輸出門店數據的結構，以便調試
        if (stores.length > 0) {
          console.log('第一個門店的結構:', JSON.stringify(stores[0]));
        }
        setStoreData(stores);

        // 如果已經有選擇的門店ID
        if (selectedStoreId) {
          console.log('已有選擇的門店ID:', selectedStoreId, '，立即更新預覽值');
          // 立即更新預覽值
          updatePreviewValueWithCurrentStore();
        }
        // 如果沒有選擇的門店ID，但有門店數據
        else if (stores.length > 0) {
          console.log('沒有選擇的門店ID，預設選擇第一個門店:', stores[0].id);
          // 預設選擇第一個門店
          if (stores[0].id) {
            setSelectedStoreId(stores[0].id);

            // 在下一個渲染周期更新預覽值
            setTimeout(() => {
              console.log('設置完門店ID後更新預覽值');
              updatePreviewValueWithCurrentStore();
            }, 0);
          }
        }
      } catch (error) {
        console.error('獲取門店數據失敗:', error);
      }
    };

    fetchStoreData();
  }, [selectedDataFieldId, selectedStoreId]);

  // 添加一個新的 useEffect 來處理自動設置門店 ID 的邏輯
  useEffect(() => {
    // 當有綁定時，自動設置門店ID
    if (selectedDataFieldId && !selectedStoreId) {
      // 優先使用元素中已存在的門店ID
      if (element.dataBinding?.selectedStoreId) {
        console.log('從元素中獲取門店ID:', element.dataBinding.selectedStoreId);
        setSelectedStoreId(element.dataBinding.selectedStoreId);
      }
      // 其次使用模板中的門店ID
      else if (element.templateStoreId) {
        console.log('從模板中獲取門店ID:', element.templateStoreId);
        setSelectedStoreId(element.templateStoreId);
      }
      // 最後才使用第一個門店的ID
      else if (storeData.length > 0 && storeData[0].id) {
        console.log('自動設置門店ID為第一個門店:', storeData[0].id);
        setSelectedStoreId(storeData[0].id);
      }
    }
  }, [selectedDataFieldId, storeData, selectedStoreId, element.dataBinding?.selectedStoreId, element.templateStoreId]);

  // 當從外部更新 element 時，更新選中的資料欄位和綁定選項
  // 只在三種情況下更新文字內容：
  // 1. 進入templater editor
  // 2. prefix狀態改變時
  // 3. 綁定數據改變時
  useEffect(() => {
    console.log("元素更新:", element.id, "綁定狀態:", !!element.dataBinding, "舊版綁定:", !!element.dataFieldId);

    // 記錄先前的綁定狀態
    const wasBound = !!selectedDataFieldId;
    // 記錄先前的綁定欄位ID
    const prevFieldId = selectedDataFieldId;
    // 記錄先前的prefix狀態
    const prevShowPrefix = showPrefix;

    // 檢查是否是首次加載（進入templater editor）
    // 注意：這裡我們不再使用這個條件，因為首次加載已經在專門的useEffect中處理了
    // 但為了保持代碼結構一致，我們保留這個變數
    const isFirstLoad = false;

    // 檢查是否綁定數據改變
    const isBindingChanged = wasBound &&
      (prevFieldId !== (element.dataBinding?.fieldId || element.dataFieldId));

    // 檢查prefix狀態是否改變
    const isPrefixChanged = wasBound &&
      (prevShowPrefix !== (element.dataBinding?.displayOptions?.showPrefix || false));

    // 如果元素已經變更，重設選擇的門店ID
    if (element.dataBinding?.selectedStoreId !== selectedStoreId) {
      console.log('元素已變更，重設門店ID狀態');
      setSelectedStoreId(element.dataBinding?.selectedStoreId || null);
    }

    // 如果從非綁定狀態變為綁定狀態，重設門店ID
    if (!wasBound && (element.dataBinding || element.dataFieldId)) {
      setSelectedStoreId(element.dataBinding?.selectedStoreId || null);
    }

    if (element.dataBinding) {
      console.log("從 dataBinding 設置狀態:", element.dataBinding.fieldId);

      // 先設置綁定相關的狀態
      setSelectedDataFieldId(element.dataBinding.fieldId);
      setDataIndex(element.dataBinding.dataIndex);
      setShowPrefix(element.dataBinding.displayOptions?.showPrefix || false);

      // 如果元素有存儲的門店ID，則使用它
      if (element.dataBinding.selectedStoreId && !selectedStoreId) {
        console.log('從元素設置門店ID:', element.dataBinding.selectedStoreId);
        setSelectedStoreId(element.dataBinding.selectedStoreId);
      }

      // 只在三種情況下更新文字內容
      if (isFirstLoad || isBindingChanged || isPrefixChanged) {
        console.log('符合更新條件，更新預覽值');

        // 如果有門店ID，則更新預覽值
        if (element.dataBinding?.selectedStoreId || selectedStoreId) {
          // 不設置臨時值，等待真實數據加載完成後再設置
          // setPreviewValue('Text');

          // 在下一個渲染周期更新預覽值
          setTimeout(() => {
            console.log('元素加載時更新預覽值');
            updatePreviewValueWithCurrentStore();
          }, 0);
        } else if (storeData.length > 0) {
          // 如果沒有門店ID但有門店數據，預設選擇第一個門店
          console.log('沒有門店ID但有門店數據，預設選擇第一個門店:', storeData[0].id);
          if (storeData[0].id) {
            setSelectedStoreId(storeData[0].id);
          }

          // 不設置臨時值，等待真實數據加載完成後再設置
          // setPreviewValue('Text');

          // 在下一個渲染周期更新預覽值
          setTimeout(() => {
            updatePreviewValueWithCurrentStore();
          }, 0);
        } else {
          // 如果沒有門店ID且沒有門店數據，則顯示預設的Text
          setPreviewValue('Text');

          // 嘗試加載門店數據
          getAllStores().then((stores: Store[]) => {
            if (stores.length > 0) {
              setStoreData(stores);
              if (stores[0].id) {
                setSelectedStoreId(stores[0].id);
              }

              // 在下一個渲染周期更新預覽值
              setTimeout(() => {
                updatePreviewValueWithCurrentStore();
              }, 0);
            } else {
              setPreviewValue('Text');
            }
          }).catch((error: any) => {
            console.error('獲取門店數據失敗:', error);
            setPreviewValue('Text');
          });
        }
      } else {
        console.log('不符合更新條件，保持當前預覽值');
      }
    } else if (element.dataFieldId) {
      console.log("從 dataFieldId 設置狀態:", element.dataFieldId);

      // 設置綁定相關的狀態
      setSelectedDataFieldId(element.dataFieldId);
      setDataIndex(0);
      setShowPrefix(false);

      // 只在三種情況下更新文字內容
      if (isFirstLoad || isBindingChanged || isPrefixChanged) {
        console.log('符合更新條件，更新預覽值 (舊版綁定)');

        // 如果有門店ID，則更新預覽值
        if (selectedStoreId) {
          // 不設置臨時值，等待真實數據加載完成後再設置
          // setPreviewValue('Text');

          // 在下一個渲染周期更新預覽值
          setTimeout(() => {
            console.log('元素加載時更新預覽值 (舊版綁定)');
            updatePreviewValueWithCurrentStore();
          }, 0);
        } else if (storeData.length > 0) {
          // 如果沒有門店ID但有門店數據，預設選擇第一個門店
          console.log('沒有門店ID但有門店數據，預設選擇第一個門店:', storeData[0].id);
          if (storeData[0].id) {
            setSelectedStoreId(storeData[0].id);
          }

          // 不設置臨時值，等待真實數據加載完成後再設置
          // setPreviewValue('Text');

          // 在下一個渲染周期更新預覽值
          setTimeout(() => {
            updatePreviewValueWithCurrentStore();
          }, 0);
        } else {
          // 如果沒有門店ID且沒有門店數據，則顯示預設的Text
          setPreviewValue('Text');

          // 嘗試加載門店數據
          getAllStores().then((stores: Store[]) => {
            if (stores.length > 0) {
              setStoreData(stores);
              if (stores[0].id) {
                setSelectedStoreId(stores[0].id);
              }

              // 在下一個渲染周期更新預覽值
              setTimeout(() => {
                updatePreviewValueWithCurrentStore();
              }, 0);
            } else {
              setPreviewValue('Text');
            }
          }).catch((error: any) => {
            console.error('獲取門店數據失敗:', error);
            setPreviewValue('Text');
          });
        }
      } else {
        console.log('不符合更新條件，保持當前預覽值 (舊版綁定)');
      }
    } else {
      console.log("清除綁定狀態");
      setSelectedDataFieldId(null);

      // 如果先前是綁定狀態，但現在不是，且內容不是 "Text"，則設置為預設文字
      if (wasBound && element.content !== 'Text') {
        updateElement({ content: 'Text' });
        // 重設預覽值，確保文字內容欄位顯示 "Text"
        setPreviewValue(null);
      }
    }
  }, [element, selectedDataFieldId]);

  // 添加自動調整文字寬度的功能
  const calculateTextWidth = (text: string, fontSize: number, fontFamily: string): number => {
    // 創建臨時 canvas 元素來測量文字寬度
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    if (context) {
      context.font = `${fontSize}px ${fontFamily}`;
      const metrics = context.measureText(text);
      // 添加額外的寬度以確保文字能完全顯示（增加一些邊距）
      return metrics.width + 20; // 添加左右各 10px 的內邊距
    }
    return 100; // 默認寬度
  };

  // 處理資料欄位綁定變更
  const handleDataFieldChange = (fieldId: string | null) => {
    // 記錄先前的綁定狀態
    const wasBound = !!selectedDataFieldId;

    setSelectedDataFieldId(fieldId);

    // 使用 TextBinding 處理文字元件的綁定
    if (fieldId) {
      // 如果從非綁定狀態變為綁定狀態，重設門店ID
      if (!wasBound) {
        setSelectedStoreId(null);
      }

      // 綁定元素到資料欄位
      const updatedElement = TextBinding.setBinding(
        element,
        dataIndex,
        fieldId,
        showPrefix,
        selectedStoreId
      );

      // 當綁定資料欄位時，更新元素
      updateElement({
        ...updatedElement,
        // 保留舊版 dataFieldId 以向後兼容
        dataFieldId: fieldId
      });

      // 更新預覽值以反映新選擇的欄位
      updatePreviewValueWithCurrentStore();
    } else {
      console.log("執行解除綁定操作，原始元素:", JSON.stringify(element));      // 解除綁定
      const updatedElement = TextBinding.removeBinding(element);
      console.log("解除綁定後的元素:", JSON.stringify(updatedElement));

      // 確保同時清除dataBinding和dataFieldId兩種綁定方式，但保留templateStoreId
      const cleanElement = {
        ...updatedElement,
        dataBinding: undefined,  // 確保新版綁定信息被清除
        dataFieldId: null,       // 確保舊版綁定信息被清除
        content: 'Text',  // 設置為預設文字 "Text"
        // 保留templateStoreId，確保後續可以正確獲取門店ID
        templateStoreId: element.templateStoreId
      };

      console.log("清理後的元素:", JSON.stringify(cleanElement));
      updateElement(cleanElement);

      // 重設預覽值，確保文字內容欄位顯示 "Text"
      setPreviewValue(null);

      // 強制觸發一次狀態更新，確保UI刷新
      setTimeout(() => {
        console.log("強制更新selectedDataFieldId為null");
        setSelectedDataFieldId(null);
      }, 0);
    }
  };

  // 處理資料索引變更
  const handleDataIndexChange = (indexValue: string) => {
    const newIndex = parseInt(indexValue, 10);
    setDataIndex(newIndex);

    if (selectedDataFieldId) {
      const updatedElement = TextBinding.setBinding(
        element,
        newIndex,
        selectedDataFieldId,
        showPrefix,
        selectedStoreId
      );
      updateElement(updatedElement);

      // 更新預覽值以反映新的資料索引
      updatePreviewValueWithCurrentStore();
    }
  };

  // 根據當前選擇的門店和商品ID更新預覽值
  const updatePreviewValueWithCurrentStore = (usePrefix: boolean = showPrefix) => {
    // 使用類型斷言獲取 storeItemUid（兼容舊版本的 storeItemSn）
    const elementStoreItemUid = (element as any).storeItemUid || (element as any).storeItemSn;
    console.log('開始更新預覽值，元素ID:', element.id, '欄位ID:', selectedDataFieldId, '門店ID:', selectedStoreId, '商品UID:', elementStoreItemUid);

    if (!selectedDataFieldId) {
      console.log('沒有選擇欄位ID，不更新預覽值');
      return;
    }

    // 檢查是否有欄位信息，但即使找不到也繼續執行
    const field = dataFields.find(f => f.id === selectedDataFieldId);
    if (!field) {
      console.log('找不到欄位信息，欄位ID:', selectedDataFieldId, '但仍嘗試從門店數據中獲取值');
      // 不要返回，繼續執行
    }

    // 如果沒有選擇門店ID，顯示預設的Text
    if (!selectedStoreId) {
      console.log('沒有選擇門店ID，顯示預設的Text');
      setPreviewValue('Text');
      return;
    }

    // 商品ID已在函數開始時獲取

    // 使用已加載的數據進行處理
    const processWithLoadedData = (providedField?: DataField, providedStore?: Store) => {
      console.log('使用已加載的數據進行處理');

      // 找到當前門店 - 優先使用提供的門店
      const currentStore = providedStore || storeData.find(store => store.id === selectedStoreId);
      if (!currentStore) {
        console.log('找不到選擇的門店，顯示找不到門店提示');
        setPreviewValue('Text');
        return;
      }

      // 找到當前欄位 - 優先使用提供的欄位
      const currentField = providedField || dataFields.find(f => f.id === selectedDataFieldId);

      // 即使找不到欄位信息，也嘗試從門店數據中獲取值
      // 這是因為門店數據中可能有欄位，但欄位定義可能不在dataFields中
      if (!currentField) {
        console.log('找不到欄位信息，欄位ID:', selectedDataFieldId, '但仍嘗試從門店數據中獲取值');
      }

      // 如果有商品UID，則查找特定商品
      if (elementStoreItemUid && currentStore.storeSpecificData && Array.isArray(currentStore.storeSpecificData)) {
        console.log('嘗試查找特定商品:', elementStoreItemUid);

        const item = currentStore.storeSpecificData.find(i => i.uid === elementStoreItemUid);

        if (item) {
          console.log('找到商品:', item);

          if (item[selectedDataFieldId] !== undefined && item[selectedDataFieldId] !== null) {
            let displayValue = String(item[selectedDataFieldId]);

            // 根據 usePrefix 參數決定是否顯示前綴
            if (usePrefix) {
              // 無論prefix是否有值，只要勾選了usePrefix，就顯示冒號
              displayValue = currentField?.prefix ? `${currentField.prefix}: ${displayValue}` : `: ${displayValue}`;
            }

            console.log('從特定商品找到數據:', displayValue);
            setPreviewValue(displayValue);
          } else {
            console.log('商品沒有選擇的欄位數據');
            setPreviewValue('Text');
          }
        } else {
          console.log('找不到商品:', elementStoreItemUid);
          setPreviewValue('Text');
        }

        return;
      }

      // 如果沒有商品ID，則顯示門店的第一個商品數據
      console.log('沒有特定商品ID，嘗試顯示門店的第一個商品數據');

      // 檢查門店是否有數據
      let hasData = false;
      let displayValue = "";

      // 檢查門店是否有 storeSpecificData 陣列
      if (currentStore.storeSpecificData && Array.isArray(currentStore.storeSpecificData) && currentStore.storeSpecificData.length > 0) {
        // 使用第一筆門店專屬數據
        const firstStoreData = currentStore.storeSpecificData[0];

        // 檢查該筆數據是否有選擇的欄位
        if (firstStoreData && firstStoreData[selectedDataFieldId] !== undefined && firstStoreData[selectedDataFieldId] !== null) {
          displayValue = String(firstStoreData[selectedDataFieldId]);
          hasData = true;
          console.log('從 storeSpecificData 找到數據:', displayValue);

          // 更新元素的商品ID
          const updatedElement = TextBinding.setBinding(
            element,
            dataIndex,
            selectedDataFieldId,
            showPrefix,
            selectedStoreId
          );

          // 同時更新元素的 templateStoreId 屬性和商品ID
          // 使用一個臨時變量來避免 TypeScript 錯誤
          const elementUpdates: any = {
            ...updatedElement,
            templateStoreId: selectedStoreId || undefined,
            storeItemUid: firstStoreData.uid,
            storeItemSn: firstStoreData.uid // 兼容舊版本
          };
          updateElement(elementUpdates);
        }
      }

      // 如果沒有在 storeSpecificData 中找到數據，嘗試從門店根級獲取（向後兼容）
      if (!hasData && (currentStore as any)[selectedDataFieldId] !== undefined && (currentStore as any)[selectedDataFieldId] !== null) {
        displayValue = String((currentStore as any)[selectedDataFieldId]);
        hasData = true;
        console.log('從門店根級找到數據:', displayValue);
      }

      if (hasData) {
        console.log('欄位有數據，原始值:', displayValue, '是否顯示前綴:', usePrefix);

        // 根據 usePrefix 參數決定是否顯示前綴
        if (usePrefix) {
          // 無論prefix是否有值，只要勾選了usePrefix，就顯示冒號
          const prefixedValue = currentField?.prefix ? `${currentField.prefix}: ${displayValue}` : `: ${displayValue}`;
          console.log('添加前綴後的值:', prefixedValue);
          displayValue = prefixedValue;
        }

        console.log('最終更新預覽值為:', displayValue);
        setPreviewValue(displayValue);

        // 確保元素的綁定信息中包含門店ID
        if (element.dataBinding && element.dataBinding.selectedStoreId !== selectedStoreId) {
          console.log('更新元素的門店ID綁定信息，從', element.dataBinding.selectedStoreId, '到', selectedStoreId);
          const updatedElement = TextBinding.setBinding(
            element,
            dataIndex,
            selectedDataFieldId,
            showPrefix,
            selectedStoreId
          );
          updateElement(updatedElement);
        }
      } else {
        console.log('門店沒有選擇的欄位數據，顯示空白或無數據提示');
        // 如果有選擇門店ID但沒有數據，顯示空白或無數據提示
        console.log('門店沒有選擇的欄位數據，顯示預設的Text');
        setPreviewValue('Text');
      }
    };

    // 檢查數據是否已加載
    if (dataFields.length > 0 && storeData.length > 0) {
      // 如果數據已加載，直接處理
      processWithLoadedData();
    } else {
      // 不設置臨時值，等待真實數據加載完成後再設置
      // setPreviewValue('Text');

      // 使用Promise.all同時加載資料欄位和門店數據
      Promise.all([
        // 直接獲取所有資料欄位，不經過bindingCore.getBindableFields過濾
        getAllDataFields(),
        storeData.length > 0 ? Promise.resolve(storeData) : getAllStores()
      ]).then(([allFields, stores]) => {
        console.log('加載到資料欄位和門店數據，欄位數量:', allFields.length, '門店數量:', stores.length);

        // 更新狀態
        if (dataFields.length === 0) {
          // 使用 TextBinding 的過濾方法，只顯示文字元件可用的欄位類型
          const bindableFields = TextBinding.getBindableFields(allFields);
          setDataFields(bindableFields);
        }

        if (storeData.length === 0) {
          setStoreData(stores);
        }

        // 確保有門店ID
        if (!selectedStoreId && stores.length > 0) {
          const firstStoreId = stores[0].id;
          if (firstStoreId) {
            console.log('自動設置門店ID為第一個門店:', firstStoreId);
            setSelectedStoreId(firstStoreId);
          }
        }

        // 查找欄位，不管它是否可綁定
        const currentField = allFields.find(f => f.id === selectedDataFieldId);
        if (!currentField) {
          console.log('在所有欄位中找不到欄位信息，欄位ID:', selectedDataFieldId);
          setPreviewValue('Text');
          return;
        }

        // 找到當前門店
        const currentStore = stores.find(store => store.id === selectedStoreId);
        if (!currentStore) {
          console.log('找不到選擇的門店，顯示找不到門店提示');
          setPreviewValue('Text');
          return;
        }

        // 延遲處理，確保狀態已更新
        setTimeout(() => {
          // 使用找到的欄位和門店處理數據
          processWithLoadedData(currentField, currentStore);
        }, 0);
      }).catch(error => {
        console.error('加載數據失敗:', error);
        setPreviewValue('Text');
      });
    }
  };

  // 當文字內容變更時自動調整元素寬度 (僅對單行文字)
  const handleTextChange = (text: string) => {
    // 如果元素已綁定資料欄位，則不允許直接編輯內容
    if (selectedDataFieldId) {
      return;
    }

    if (!text) return updateElement({ content: '' });

    // 對於多行文字，僅更新內容而不自動調整寬度
    if (isMultilineText) {
      updateElement({ content: text });
      return;
    }

    // 對於單行文字，計算適合的寬度
    const newWidth = calculateTextWidth(
      text,
      element.fontSize || 14,
      element.fontFamily || 'Arial'
    );

    // 更新元素內容和寬度
    updateElement({
      content: text,
      width: Math.max(newWidth, 50) // 確保最小寬度為 50px
    });
  };

  // 判斷元素是否已綁定資料 - 在其他地方直接使用 !!selectedDataFieldId

  // 檢查資料綁定有效性
  const checkDataBindingValidity = (availableFields: DataField[]) => {
    // 如果元素沒有綁定資料欄位，直接返回
    if (!element.dataBinding && !element.dataFieldId) {
      return;
    }

    let fieldId = element.dataBinding?.fieldId || element.dataFieldId;
    let currentDataIndex = element.dataBinding?.dataIndex || 0;
    let isBindingValid = true;
    let invalidReason = '';

    // 檢查 1: 檢查欄位 ID 是否存在於可用欄位中
    if (fieldId) {
      const fieldExists = availableFields.some(field => field.id === fieldId);
      if (!fieldExists) {
        isBindingValid = false;
        invalidReason = `欄位ID "${fieldId}" 已不存在`;
        console.warn(`綁定的欄位ID "${fieldId}" 在可用欄位中不存在，將解除綁定`);
      }
    }

    // 檢查 2: 檢查資料索引是否有效
    if (isBindingValid && currentDataIndex >= maxBindingDataCount) {
      isBindingValid = false;
      invalidReason = `資料索引 ${currentDataIndex + 1} 超出有效範圍 (1-${maxBindingDataCount})`;
      console.warn(`綁定的資料索引 ${currentDataIndex} 超出有效範圍 (0-${maxBindingDataCount - 1})，將解除綁定`);
    }

    // 如果綁定無效，自動解除綁定
    if (!isBindingValid) {
      console.log(`自動解除綁定，原因: ${invalidReason}`);

      // 使用 TextBinding 解除綁定
      const updatedElement = TextBinding.removeBinding(element);

      // 確保同時清除 dataBinding 和 dataFieldId，但保留templateStoreId
      const cleanElement = {
        ...updatedElement,
        dataBinding: undefined,  // 清除新版綁定信息
        dataFieldId: null,       // 清除舊版綁定信息
        content: 'Text',  // 設置為預設文字 "Text"
        // 保留templateStoreId，確保後續可以正確獲取門店ID
        templateStoreId: element.templateStoreId
      };

      // 更新元素
      updateElement(cleanElement);

      // 重設預覽值，確保文字內容欄位顯示 "Text"
      setPreviewValue(null);

      // 更新本地狀態
      setSelectedDataFieldId(null);
      setDataIndex(0);
      setShowPrefix(false);
    }
  };
  // 生成預覽選項 - 顯示選定門店的商品數據
  const generatePreviewOptions = () => {
    if (!selectedDataFieldId) return [];

    const field = dataFields.find(f => f.id === selectedDataFieldId);
    if (!field) return [];

    // 顯示所有門店數據選項
    const options = [];

    // 首先找到選定的門店
    const selectedStore = storeData.find(store => store.id === selectedStoreId);

    if (!selectedStore) {
      console.log('找不到選定的門店:', selectedStoreId);
      options.push({
        value: '',
        label: `找不到門店 ${selectedStoreId}`
      });
      return options;
    }

    // 檢查門店是否有 storeSpecificData 陣列
    if (!selectedStore.storeSpecificData || !Array.isArray(selectedStore.storeSpecificData) || selectedStore.storeSpecificData.length === 0) {
      console.log('門店沒有商品數據:', selectedStoreId);
      options.push({
        value: '',
        label: `門店 ${selectedStore.name || selectedStore.id} 沒有商品數據`
      });
      return options;
    }

    // 為門店的每個商品數據添加選項
    selectedStore.storeSpecificData.forEach((item, index) => {
      if (item && item.uid) {
        let displayValue = "(無數據)";
        let hasData = false;

        // 檢查該筆數據是否有選擇的欄位
        if (item[selectedDataFieldId] !== undefined && item[selectedDataFieldId] !== null) {
          displayValue = String(item[selectedDataFieldId]);
          hasData = true;
        }

        // 構建顯示標籤 - 使用商品名稱或ID作為識別
        const itemLabel = item.name || item.id || `商品 ${index + 1}`;

        // 顯示除錯信息
        console.log(`商品 ${index + 1} (UID: ${item.uid}) 數據預覽:`, displayValue, '有數據:', hasData);

        options.push({
          value: `item:${item.uid}`,
          label: `${itemLabel} : ${displayValue}`
        });
      }
    });

    // 如果沒有商品數據，添加提示
    if (options.length === 0) {
      options.push({
        value: '',
        label: `門店 ${selectedStore.name || selectedStore.id} 沒有可用的商品數據`
      });
    }

    return options;
  };
    // 處理預覽選擇 - 處理商品數據
  const handlePreviewSelect = (value: string) => {
    const field = selectedDataFieldId ? dataFields.find(f => f.id === selectedDataFieldId) : null;

    if (!field) {
      return;
    }

    if (!value) {
      // 沒有選擇商品，顯示預設的Text
      setPreviewValue('Text');

      // 更新元素的綁定信息，清除選擇的商品ID，但保留門店ID和templateStoreId
      if (selectedDataFieldId) {
        const updatedElement = TextBinding.setBinding(
          element,
          dataIndex,
          selectedDataFieldId,
          showPrefix,
          selectedStoreId
        );

        // 保留templateStoreId，確保後續可以正確獲取門店ID
        // 使用一個臨時變量來避免 TypeScript 錯誤
        const elementUpdates: any = {
          ...updatedElement,
          templateStoreId: element.templateStoreId,
          // 清除商品UID綁定
          storeItemUid: null,
          storeItemSn: null // 兼容舊版本
        };
        updateElement(elementUpdates);
      }

      return;
    }

    if (value.startsWith('item:') && selectedDataFieldId) {
      // 顯示特定商品的資料
      const itemUid = value.split(':')[1];

      // 找到選定的門店
      const store = storeData.find(s => s.id === selectedStoreId);
      if (!store || !store.storeSpecificData) {
        console.log('找不到門店或門店沒有商品數據');
        setPreviewValue('Text');
        return;
      }

      // 找到選定的商品
      const item = store.storeSpecificData.find(i => i.uid === itemUid);
      if (!item) {
        console.log('找不到商品:', itemUid);
        setPreviewValue('Text');
        return;
      }

      // 檢查商品是否有選擇的欄位
      if (item[selectedDataFieldId] !== undefined && item[selectedDataFieldId] !== null) {
        let displayValue = String(item[selectedDataFieldId]);

        // 根據前綴設置決定是否顯示前綴
        if (showPrefix) {
          // 無論prefix是否有值，只要勾選了showPrefix，就顯示冒號
          displayValue = field.prefix ? `${field.prefix}: ${displayValue}` : `: ${displayValue}`;
        }

        console.log('更新預覽值為:', displayValue);
        setPreviewValue(displayValue);
      } else {
        console.log('商品沒有選擇的欄位數據');
        setPreviewValue('Text');
      }

      // 更新元素的綁定信息，保存選擇的門店ID和商品ID
      if (selectedDataFieldId) {
        const updatedElement = TextBinding.setBinding(
          element,
          dataIndex,
          selectedDataFieldId,
          showPrefix,
          selectedStoreId
        );

        // 同時更新元素的 templateStoreId 屬性和商品ID
        // 使用一個臨時變量來避免 TypeScript 錯誤
        const elementUpdates: any = {
          ...updatedElement,
          templateStoreId: selectedStoreId || undefined,
          // 添加商品UID綁定
          storeItemUid: itemUid,
          storeItemSn: itemUid // 兼容舊版本
        };
        updateElement(elementUpdates);
      }
    } else {
      // 回到默認
      setPreviewValue('Text');
    }
  };

  return (
    <div className="space-y-3">
      {/* 1. 綁定資料欄位 */}
      <FormField label="綁定資料欄位">
        <select
          className="w-full p-2 bg-gray-700 rounded text-white border border-gray-600 focus:border-blue-500"
          value={selectedDataFieldId || ''}
          onChange={(e) => {
            const value = e.target.value;
            handleDataFieldChange(value === '' ? null : value);
          }}
          disabled={isLoadingDataFields}
        >
          <option value="">無綁定</option>
          {dataFields.map(field => (
            <option key={field.id} value={field.id}>
              {field.name} ({field.type})
            </option>
          ))}
        </select>
        {isLoadingDataFields && (
          <div className="mt-1 text-sm text-gray-400">載入資料欄位中...</div>
        )}
      </FormField>

      {/* 2. 資料索引 - 只在有綁定時顯示 */}
      {selectedDataFieldId && (
        <FormField label="資料索引">
          <select
            className="w-full p-2 bg-gray-700 rounded text-white border border-gray-600 focus:border-blue-500"
            value={dataIndex.toString()}
            onChange={(e) => handleDataIndexChange(e.target.value)}
          >
            {dataIndexOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </FormField>
      )}

      {/* 3. 文字內容 */}
      <FormField label="文字內容">
        <TextInput
          value={selectedDataFieldId
            ? (previewValue !== null ? previewValue : 'Text')
            : (element.content || '')}
          onChange={handleTextChange}
          rows={isMultilineText ? 3 : 1}
          disabled={!!selectedDataFieldId} // 當綁定資料欄位時禁用編輯
          isBound={!!selectedDataFieldId}
        />
      </FormField>

      {/* 提示訊息 - 顯示在文字內容下方，對齊標題位置 */}
      {selectedDataFieldId && (
        <div className="text-sm text-red-400 -mt-2">
          <span>已綁定資料欄位，顯示佔位符。將在預覽時替換為實際資料。</span>
        </div>
      )}

      {/* 4. 預覽數據 - 只在有綁定時顯示 */}
      {selectedDataFieldId && (
        <FormField label="預覽數據">
          <select
            className="w-full p-2 bg-gray-700 rounded text-white border border-gray-600 focus:border-blue-500"
            value={(element as any).storeItemUid || (element as any).storeItemSn ? `item:${(element as any).storeItemUid || (element as any).storeItemSn}` : ''}
            onChange={(e) => handlePreviewSelect(e.target.value)}
          >
            <option value="">選擇預覽數據</option>
            {generatePreviewOptions().map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </FormField>
      )}

      {/* 5. 顯示前綴選項 - 只在有綁定時顯示 */}
      {selectedDataFieldId && (
        <FormField label="顯示選項">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="showPrefix"
              checked={showPrefix}
              onChange={(e) => {
                const newShowPrefix = e.target.checked;
                setShowPrefix(newShowPrefix);

                if (selectedDataFieldId) {
                  // 更新元素綁定
                  const updatedElement = TextBinding.setBinding(
                    element,
                    dataIndex,
                    selectedDataFieldId,
                    newShowPrefix,
                    selectedStoreId
                  );
                  updateElement(updatedElement);

                  // 更新預覽值以反映前綴顯示狀態變化
                  updatePreviewValueWithCurrentStore(newShowPrefix);
                }
              }}
              className="mr-2"
            />
            <label htmlFor="showPrefix" className="text-gray-300 text-sm">
              顯示前綴
            </label>
          </div>
        </FormField>
      )}

      <FormField label="字體大小">
        <NumberInput
          value={element.fontSize || 14}
          onChange={(value) => {
            if (isMultilineText) {
              // 多行文字只更新字體大小，不調整寬度
              updateElement({ fontSize: value });
            } else {
              // 單行文字會根據字體大小調整寬度
              const newWidth = element.content
                ? calculateTextWidth(element.content, value, element.fontFamily || 'Arial')
                : element.width;
              updateElement({
                fontSize: value,
                width: Math.max(newWidth, 50)
              });
            }
          }}
        />
      </FormField>
      <FormField label="字體">
        <SelectInput
          value={element.fontFamily || 'Arial'}
          onChange={(value) => {
            if (isMultilineText) {
              // 多行文字只更新字體類型，不調整寬度
              updateElement({ fontFamily: value });
            } else {
              // 單行文字會根據字體類型調整寬度
              const newWidth = element.content
                ? calculateTextWidth(element.content, element.fontSize || 14, value)
                : element.width;
              updateElement({
                fontFamily: value,
                width: Math.max(newWidth, 50)
              });
            }
          }}
          options={fontOptions}
        />
      </FormField>

      {/* 添加線條寬度和顏色設置 */}
      <FormField label="線條寬度">
        <NumberInput
          value={element.lineWidth || 1}
          onChange={(value) => updateElement({ lineWidth: value })}
          min={0.5}
          max={5}
          step={0.5}
        />
      </FormField>
      <FormField label="文字顏色">
        <RestrictedColorInput
          value={element.lineColor || '#000000'}
          onChange={(value) => updateElement({ lineColor: value })}
          colorType={colorType}
        />
      </FormField>
    </div>
  );
};