# 過濾功能使用指南

## 功能概述
EPD Manager App 的網關管理和設備管理頁面現在都支持過濾功能，讓用戶可以快速查看特定狀態的設備或網關。

## 網關管理頁面過濾

### 過濾選項
- **全部 (X)**: 顯示所有網關，X 為總數量
- **在線 (Y)**: 只顯示在線狀態的網關，Y 為在線數量
- **離線 (Z)**: 只顯示離線狀態的網關，Z 為離線數量

### 使用方法
1. 進入網關管理頁面
2. 在統計卡片下方會看到過濾標籤
3. 點擊任意標籤即可切換過濾條件
4. 選中的標籤會高亮顯示
5. 列表標題會顯示過濾後的數量

### 功能特點
- 即時過濾：點擊標籤立即生效
- 數量顯示：每個標籤顯示對應類別的數量
- 視覺反饋：選中標籤使用主色調高亮
- 空狀態處理：當過濾結果為空時顯示友好提示

## 設備管理頁面過濾

### 過濾選項
- **全部 (X)**: 顯示所有設備，X 為總數量
- **在線 (Y)**: 只顯示在線狀態的設備，Y 為在線數量
- **離線 (Z)**: 只顯示離線狀態的設備，Z 為離線數量

### 使用方法
1. 進入設備管理頁面
2. 在統計卡片下方會看到過濾標籤
3. 點擊任意標籤即可切換過濾條件
4. 選中的標籤會高亮顯示
5. 列表標題會顯示過濾後的數量

## 技術實現亮點

### 性能優化
- 使用 `useMemo` 避免不必要的重新計算
- 過濾邏輯高效，不影響應用性能

### 即時更新
- 當 WebSocket 接收到新數據時，過濾結果會自動更新
- 數量統計會即時反映最新狀態

### 用戶體驗
- 響應式設計，適配不同屏幕尺寸
- 一致的交互模式，降低學習成本
- 清晰的視覺層次，易於理解和使用

## 常見問題

### Q: 為什麼有時候過濾標籤不顯示？
A: 只有當存在設備或網關時，過濾標籤才會顯示。如果列表為空，過濾標籤會隱藏。

### Q: 過濾後的數據會自動更新嗎？
A: 是的，當 WebSocket 接收到新的狀態更新時，過濾結果會自動刷新。

### Q: 可以同時選擇多個過濾條件嗎？
A: 目前版本只支持單選過濾。未來版本可能會支持多選功能。

## 未來擴展計劃

1. **更多過濾條件**
   - 設備型號過濾
   - 固件版本過濾
   - 電量範圍過濾

2. **高級過濾功能**
   - 多選過濾
   - 自定義過濾條件
   - 保存過濾偏好

3. **搜索結合過濾**
   - 文字搜索 + 狀態過濾
   - 模糊搜索支持

4. **過濾歷史**
   - 記住用戶的過濾偏好
   - 快速切換常用過濾條件
