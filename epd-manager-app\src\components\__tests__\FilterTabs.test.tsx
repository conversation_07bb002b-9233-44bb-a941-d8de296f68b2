import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { FilterTabs, FilterOption } from '../FilterTabs';

describe('FilterTabs', () => {
  const mockOptions: FilterOption[] = [
    { key: 'all', label: '全部', count: 10 },
    { key: 'online', label: '在線', count: 7 },
    { key: 'offline', label: '離線', count: 3 },
  ];

  const mockOnFilterChange = jest.fn();

  beforeEach(() => {
    mockOnFilterChange.mockClear();
  });

  it('renders all filter options', () => {
    const { getByText } = render(
      <FilterTabs
        options={mockOptions}
        selectedFilter="all"
        onFilterChange={mockOnFilterChange}
      />
    );

    expect(getByText('全部 (10)')).toBeTruthy();
    expect(getByText('在線 (7)')).toBeTruthy();
    expect(getByText('離線 (3)')).toBeTruthy();
  });

  it('calls onFilterChange when a tab is pressed', () => {
    const { getByText } = render(
      <FilterTabs
        options={mockOptions}
        selectedFilter="all"
        onFilterChange={mockOnFilterChange}
      />
    );

    fireEvent.press(getByText('在線 (7)'));
    expect(mockOnFilterChange).toHaveBeenCalledWith('online');
  });

  it('highlights the selected filter', () => {
    const { getByText } = render(
      <FilterTabs
        options={mockOptions}
        selectedFilter="online"
        onFilterChange={mockOnFilterChange}
      />
    );

    // The selected tab should have different styling
    // This is a basic test - in a real scenario you'd test the actual styles
    expect(getByText('在線 (7)')).toBeTruthy();
  });
});
