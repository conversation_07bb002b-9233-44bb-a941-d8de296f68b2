@echo off
echo EPD Manager App Android 診斷工具
echo ===============================
cd /d "d:\CODE\git\epd-manager-lite\epd-manager-app"

REM 檢查環境變數
echo 正在檢查環境變數...
if not defined ANDROID_HOME (
    echo [錯誤] 未設置 ANDROID_HOME 環境變數
    echo 請安裝 Android SDK 並設置 ANDROID_HOME 環境變數
) else (
    echo [成功] 已找到 ANDROID_HOME: %ANDROID_HOME%
)

if not defined JAVA_HOME (
    echo [錯誤] 未設置 JAVA_HOME 環境變數
    echo 請安裝 JDK 並設置 JAVA_HOME 環境變數
) else (
    echo [成功] 已找到 JAVA_HOME: %JAVA_HOME%
)

REM 檢查 Android SDK 工具
echo.
echo 正在檢查 Android SDK 工具...
if not exist "%ANDROID_HOME%\platform-tools\adb.exe" (
    echo [錯誤] 未找到 adb 工具
    echo 請安裝完整的 Android SDK
) else (
    echo [成功] 已找到 adb 工具
)

if not exist "%ANDROID_HOME%\tools\bin\sdkmanager.bat" (
    echo [警告] 未找到 SDK Manager 工具
) else (
    echo [成功] 已找到 SDK Manager 工具
)

REM 檢查 Node.js 和 npm
echo.
echo 正在檢查 Node.js 和 npm...
node --version > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [錯誤] 未找到 Node.js
    echo 請安裝 Node.js
) else (
    echo [成功] 已找到 Node.js: 
    node --version
)

npm --version > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [錯誤] 未找到 npm
    echo 請安裝 npm
) else (
    echo [成功] 已找到 npm: 
    npm --version
)

REM 檢查 Expo CLI
echo.
echo 正在檢查 Expo CLI...
call npx expo --version > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [錯誤] 未找到 Expo CLI
    echo 請安裝 Expo CLI: npm install -g expo-cli
) else (
    echo [成功] 已找到 Expo CLI
    call npx expo --version
)

REM 檢查 package.json 依賴
echo.
echo 正在檢查 package.json 依賴...
if not exist "package.json" (
    echo [錯誤] 未找到 package.json 文件
) else (
    echo [成功] 已找到 package.json 文件
    echo 正在檢查缺失的依賴...
    
    REM 嘗試運行 npm list 來檢查缺失的依賴
    call npm list --depth=0 > npm_list_output.txt 2>&1
    findstr /C:"UNMET DEPENDENCY" npm_list_output.txt > nul
    if %ERRORLEVEL% EQU 0 (
        echo [警告] 發現缺失的依賴項，請運行 npm install
        type npm_list_output.txt | findstr /C:"UNMET DEPENDENCY"
    ) else (
        echo [成功] 未發現缺失的依賴項
    )
    del npm_list_output.txt
)

REM 檢查源代碼問題
echo.
echo 正在檢查源代碼問題...

REM 檢查 App.tsx 中的 hooks 導入
if exist "App.tsx" (
    echo [成功] 已找到 App.tsx 文件
    findstr /C:"import { useAuth } from './src/stores/authStore';" App.tsx > nul
    if %ERRORLEVEL% EQU 0 (
        echo [檢查] App.tsx 導入了 useAuth
        
        REM 檢查 authStore.ts 中是否導出了 useAuth
        if exist "src\stores\authStore.ts" (
            echo [成功] 已找到 authStore.ts 文件
            findstr /C:"export const useAuth" src\stores\authStore.ts > nul
            if %ERRORLEVEL% NEQ 0 (
                echo [錯誤] authStore.ts 中缺少 useAuth 函數導出
                echo 您需要添加 useAuth 函數導出到 authStore.ts 文件
            ) else (
                echo [成功] authStore.ts 中存在 useAuth 函數導出
            )
        ) else (
            echo [錯誤] 未找到 src\stores\authStore.ts 文件
        )
    )
) else (
    echo [錯誤] 未找到 App.tsx 文件
)

REM 檢查連接的設備
echo.
echo 正在檢查連接的 Android 設備...
"%ANDROID_HOME%\platform-tools\adb.exe" devices > adb_devices_output.txt
type adb_devices_output.txt
findstr /C:"device" adb_devices_output.txt > nul
if %ERRORLEVEL% NEQ 0 (
    echo [錯誤] 未找到連接的 Android 設備
    echo 請確保您的 Android 設備已連接且已啟用 USB 調試
) else (
    echo [成功] 已找到連接的 Android 設備
)
del adb_devices_output.txt

REM 建議修復步驟
echo.
echo 診斷完成，建議的修復步驟：
echo 1. 確保所有環境變數正確設置
echo 2. 運行 'npm install' 安裝所有依賴
echo 3. 檢查 storeStore.ts 和 authStore.ts 中的 hooks 導出
echo 4. 重新構建和安裝應用：
echo    npm run prebuild
echo    npm run android
echo.
echo 5. 或者嘗試執行 Debug 版本：
echo    npm run android:debug

echo.
echo 如果問題仍然存在，請考慮查看以下文件：
echo - App.tsx 的啟動邏輯
echo - authStore.ts 中的 useAuth hook
echo - src\services\ApiService.ts 中的 API 配置

echo.
echo 注意：確保您的設備已連接並啟用 USB 調試

pause
cd android
call gradlew.bat clean --info
cd ..

echo 開始詳細構建:
cd android
call gradlew.bat app:assembleDebug --stacktrace --info -x lint -x test
cd ..

echo 診斷完成
