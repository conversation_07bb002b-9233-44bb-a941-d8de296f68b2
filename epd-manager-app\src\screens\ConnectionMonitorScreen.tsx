// EPD Manager App - 連線監控頁面

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useGateways } from '../stores/gatewayStore';
import { webSocketService } from '../services/WebSocketService';
import { COLORS, SIZES, CONNECTION_STATUS } from '../utils/constants';

interface LogMessage {
  id: string;
  timestamp: Date;
  type: 'sent' | 'received' | 'info' | 'error';
  message: string;
  data?: any;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SIZES.SPACING_MD,
    backgroundColor: COLORS.SURFACE,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.TEXT_DISABLED,
  },
  title: {
    fontSize: SIZES.FONT_SIZE_XL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  headerActions: {
    flexDirection: 'row',
    gap: SIZES.SPACING_SM,
  },
  actionButton: {
    backgroundColor: COLORS.INFO,
    paddingHorizontal: SIZES.SPACING_MD,
    paddingVertical: SIZES.SPACING_SM,
    borderRadius: SIZES.BORDER_RADIUS_SM,
  },
  actionButtonText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_SM,
    fontWeight: '600',
  },
  actionButtonTextDisabled: {
    color: COLORS.TEXT_DISABLED,
  },
  statusCard: {
    backgroundColor: COLORS.SURFACE,
    margin: SIZES.SPACING_MD,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    padding: SIZES.SPACING_MD,
  },
  statusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.SPACING_SM,
  },
  statusTitle: {
    fontSize: SIZES.FONT_SIZE_LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  statusIndicator: {
    paddingHorizontal: SIZES.SPACING_MD,
    paddingVertical: SIZES.SPACING_SM,
    borderRadius: SIZES.BORDER_RADIUS_SM,
  },
  statusText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_SM,
    fontWeight: '600',
  },
  gatewayInfo: {
    paddingTop: SIZES.SPACING_SM,
    borderTopWidth: 1,
    borderTopColor: COLORS.TEXT_DISABLED,
  },
  gatewayName: {
    fontSize: SIZES.FONT_SIZE_MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SIZES.SPACING_XS,
  },
  gatewayMac: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
    fontFamily: 'monospace',
    marginBottom: SIZES.SPACING_XS,
  },
  gatewayModel: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
  },
  noGatewayText: {
    fontSize: SIZES.FONT_SIZE_MD,
    color: COLORS.TEXT_DISABLED,
    textAlign: 'center',
    paddingVertical: SIZES.SPACING_LG,
  },
  logControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SIZES.SPACING_MD,
    paddingVertical: SIZES.SPACING_SM,
    backgroundColor: COLORS.SURFACE,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.TEXT_DISABLED,
  },
  logTitle: {
    fontSize: SIZES.FONT_SIZE_MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  autoScrollButton: {
    paddingHorizontal: SIZES.SPACING_SM,
    paddingVertical: SIZES.SPACING_XS,
    borderRadius: SIZES.BORDER_RADIUS_SM,
    borderWidth: 1,
    borderColor: COLORS.TEXT_DISABLED,
  },
  autoScrollButtonActive: {
    backgroundColor: COLORS.PRIMARY,
    borderColor: COLORS.PRIMARY,
  },
  autoScrollText: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
  },
  autoScrollTextActive: {
    color: COLORS.SURFACE,
    fontWeight: '600',
  },
  logContainer: {
    flex: 1,
    padding: SIZES.SPACING_SM,
  },
  logItem: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: SIZES.BORDER_RADIUS_SM,
    padding: SIZES.SPACING_SM,
    marginBottom: SIZES.SPACING_SM,
  },
  logHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SIZES.SPACING_XS,
  },
  logIcon: {
    fontSize: SIZES.FONT_SIZE_SM,
    marginRight: SIZES.SPACING_SM,
  },
  logTime: {
    fontSize: SIZES.FONT_SIZE_XS,
    color: COLORS.TEXT_DISABLED,
    fontFamily: 'monospace',
    marginRight: SIZES.SPACING_SM,
  },
  logTypeBadge: {
    paddingHorizontal: SIZES.SPACING_SM,
    paddingVertical: 2,
    borderRadius: SIZES.BORDER_RADIUS_SM,
    marginLeft: 'auto',
  },
  logTypeText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_XS,
    fontWeight: '600',
  },
  logMessage: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SIZES.SPACING_XS,
  },
  logData: {
    fontSize: SIZES.FONT_SIZE_XS,
    color: COLORS.TEXT_SECONDARY,
    fontFamily: 'monospace',
    backgroundColor: COLORS.BACKGROUND,
    padding: SIZES.SPACING_SM,
    borderRadius: SIZES.BORDER_RADIUS_SM,
  },
  emptyContainer: {
    alignItems: 'center',
    padding: SIZES.SPACING_XL,
  },
  emptyText: {
    fontSize: SIZES.FONT_SIZE_LG,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SIZES.SPACING_SM,
  },
  emptySubtext: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_DISABLED,
    textAlign: 'center',
  },
});

export const ConnectionMonitorScreen: React.FC = () => {
  const [refreshing, setRefreshing] = useState(false);
  const [logs, setLogs] = useState<LogMessage[]>([]);
  const [autoScroll, setAutoScroll] = useState(true);
  const scrollViewRef = useRef<ScrollView>(null);
  
  const {
    selectedGateway,
    connectionStatus,
    isConnected,
    getCurrentGateway
  } = useGateways();

  // 添加日誌消息
  const addLog = (type: LogMessage['type'], message: string, data?: any) => {
    const newLog: LogMessage = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date(),
      type,
      message,
      data
    };

    setLogs(prev => {
      const updated = [...prev, newLog];
      // 保持最多 100 條日誌
      return updated.slice(-100);
    });

    // 自動滾動到底部
    if (autoScroll) {
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  };

  // 設置 WebSocket 消息監聽器
  useEffect(() => {
    // 監聽所有 WebSocket 消息
    const handleMessage = (message: any) => {
      addLog('received', `收到消息: ${message.type}`, message);
    };

    // 監聽狀態變化
    const handleStatusChange = (status: any) => {
      const statusText = getStatusText(status);
      addLog('info', `連接狀態變更: ${statusText}`);
    };

    // 只有在真實連接時才監聽WebSocket消息
    if (isConnected) {
      webSocketService.addMessageHandler('all', handleMessage);
      webSocketService.addStatusChangeHandler(handleStatusChange);
    }

    // 添加初始日誌
    addLog('info', '連線監控已啟動');

    if (selectedGateway) {
      addLog('info', `當前網關: ${selectedGateway.name} (${selectedGateway.macAddress})`);
    } else {
      addLog('info', '未連接到任何網關');
    }

    return () => {
      if (isConnected) {
        webSocketService.removeMessageHandler('all');
        webSocketService.removeStatusChangeHandler(handleStatusChange);
      }
    };
  }, [isConnected, selectedGateway]);

  const getStatusText = (status: string) => {
    switch (status) {
      case CONNECTION_STATUS.CONNECTED:
        return '已連接';
      case CONNECTION_STATUS.CONNECTING:
        return '連接中';
      case CONNECTION_STATUS.DISCONNECTED:
        return '已斷開';
      case CONNECTION_STATUS.ERROR:
        return '連接錯誤';
      default:
        return '未知狀態';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case CONNECTION_STATUS.CONNECTED:
        return COLORS.SUCCESS;
      case CONNECTION_STATUS.CONNECTING:
        return COLORS.WARNING;
      case CONNECTION_STATUS.DISCONNECTED:
        return COLORS.TEXT_DISABLED;
      case CONNECTION_STATUS.ERROR:
        return COLORS.ERROR;
      default:
        return COLORS.TEXT_DISABLED;
    }
  };

  const getLogTypeColor = (type: LogMessage['type']) => {
    switch (type) {
      case 'sent':
        return COLORS.INFO;
      case 'received':
        return COLORS.SUCCESS;
      case 'info':
        return COLORS.TEXT_SECONDARY;
      case 'error':
        return COLORS.ERROR;
      default:
        return COLORS.TEXT_SECONDARY;
    }
  };

  const getLogTypeIcon = (type: LogMessage['type']) => {
    switch (type) {
      case 'sent':
        return '↗️';
      case 'received':
        return '↙️';
      case 'info':
        return 'ℹ️';
      case 'error':
        return '❌';
      default:
        return '📝';
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    addLog('info', '手動刷新監控數據');
    setTimeout(() => setRefreshing(false), 1000);
  };

  const handleClearLogs = () => {
    Alert.alert(
      '清除日誌',
      '確定要清除所有日誌記錄嗎？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '清除',
          style: 'destructive',
          onPress: () => {
            setLogs([]);
            addLog('info', '日誌已清除');
          }
        }
      ]
    );
  };

  const handleTestConnection = () => {
    if (isConnected && selectedGateway) {
      addLog('sent', '發送測試 ping 消息');
      // 發送測試消息到WebSocket
      try {
        const testMessage = {
          type: 'ping',
          timestamp: Date.now(),
          test: true
        };
        // 這裡可以通過WebSocket服務發送測試消息
        addLog('info', '測試消息已發送');
      } catch (error: any) {
        addLog('error', `發送測試消息失敗: ${error.message}`);
      }
    } else {
      Alert.alert('提示', '請先連接到網關');
      addLog('error', '無法發送測試消息：未連接到網關');
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('zh-TW', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const currentGateway = getCurrentGateway();

  return (
    <SafeAreaView style={styles.container}>
      {/* 頭部狀態 */}
      <View style={styles.header}>
        <Text style={styles.title}>連線監控</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleTestConnection}
            disabled={!isConnected}
          >
            <Text style={[
              styles.actionButtonText,
              !isConnected && styles.actionButtonTextDisabled
            ]}>
              測試
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleClearLogs}
          >
            <Text style={styles.actionButtonText}>清除</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* 連接狀態卡片 */}
      <View style={styles.statusCard}>
        <View style={styles.statusHeader}>
          <Text style={styles.statusTitle}>連接狀態</Text>
          <View style={[
            styles.statusIndicator,
            { backgroundColor: getStatusColor(connectionStatus) }
          ]}>
            <Text style={styles.statusText}>
              {getStatusText(connectionStatus)}
            </Text>
          </View>
        </View>

        {currentGateway && (
          <View style={styles.gatewayInfo}>
            <Text style={styles.gatewayName}>
              網關: {currentGateway.name}
            </Text>
            <Text style={styles.gatewayMac}>
              MAC: {currentGateway.macAddress}
            </Text>
            <Text style={styles.gatewayModel}>
              型號: {currentGateway.model || 'GW-2000'}
            </Text>
          </View>
        )}

        {!currentGateway && (
          <Text style={styles.noGatewayText}>
            未連接到任何網關
          </Text>
        )}
      </View>

      {/* 日誌控制 */}
      <View style={styles.logControls}>
        <Text style={styles.logTitle}>
          通信日誌 ({logs.length})
        </Text>
        <TouchableOpacity
          style={[
            styles.autoScrollButton,
            autoScroll && styles.autoScrollButtonActive
          ]}
          onPress={() => setAutoScroll(!autoScroll)}
        >
          <Text style={[
            styles.autoScrollText,
            autoScroll && styles.autoScrollTextActive
          ]}>
            自動滾動
          </Text>
        </TouchableOpacity>
      </View>

      {/* 日誌列表 */}
      <ScrollView
        ref={scrollViewRef}
        style={styles.logContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[COLORS.PRIMARY]}
          />
        }
      >
        {logs.map((log) => (
          <View key={log.id} style={styles.logItem}>
            <View style={styles.logHeader}>
              <Text style={styles.logIcon}>
                {getLogTypeIcon(log.type)}
              </Text>
              <Text style={styles.logTime}>
                {formatTime(log.timestamp)}
              </Text>
              <View style={[
                styles.logTypeBadge,
                { backgroundColor: getLogTypeColor(log.type) }
              ]}>
                <Text style={styles.logTypeText}>
                  {log.type.toUpperCase()}
                </Text>
              </View>
            </View>
            <Text style={styles.logMessage}>
              {log.message}
            </Text>
            {log.data && (
              <Text style={styles.logData}>
                {JSON.stringify(log.data, null, 2)}
              </Text>
            )}
          </View>
        ))}

        {logs.length === 0 && (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>暫無日誌記錄</Text>
            <Text style={styles.emptySubtext}>
              連接到網關後將顯示通信日誌
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};
