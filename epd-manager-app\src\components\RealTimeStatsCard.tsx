// EPD Manager App - 即時統計卡片組件

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { COLORS, SIZES } from '../utils/constants';

interface StatItem {
  label: string;
  value: string | number;
  icon: string;
  color: string;
  trend?: 'up' | 'down' | 'stable';
  trendValue?: string;
}

interface RealTimeStatsCardProps {
  title: string;
  stats: StatItem[];
  onPress?: () => void;
  style?: any;
}

export const RealTimeStatsCard: React.FC<RealTimeStatsCardProps> = ({
  title,
  stats,
  onPress,
  style,
}) => {
  const getTrendIcon = (trend?: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return '📈';
      case 'down':
        return '📉';
      case 'stable':
        return '➡️';
      default:
        return '';
    }
  };

  const getTrendColor = (trend?: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return COLORS.SUCCESS;
      case 'down':
        return COLORS.ERROR;
      case 'stable':
        return COLORS.WARNING;
      default:
        return COLORS.TEXT_SECONDARY;
    }
  };

  const CardContent = () => (
    <View style={[styles.container, style]}>
      <Text style={styles.title}>{title}</Text>
      
      <View style={styles.statsGrid}>
        {stats.map((stat, index) => (
          <View key={index} style={styles.statItem}>
            <View style={styles.statHeader}>
              <Text style={styles.statIcon}>{stat.icon}</Text>
              {stat.trend && (
                <View style={styles.trendContainer}>
                  <Text style={styles.trendIcon}>{getTrendIcon(stat.trend)}</Text>
                  {stat.trendValue && (
                    <Text style={[styles.trendValue, { color: getTrendColor(stat.trend) }]}>
                      {stat.trendValue}
                    </Text>
                  )}
                </View>
              )}
            </View>
            
            <Text style={[styles.statValue, { color: stat.color }]}>
              {stat.value}
            </Text>
            <Text style={styles.statLabel}>{stat.label}</Text>
          </View>
        ))}
      </View>
    </View>
  );

  if (onPress) {
    return (
      <TouchableOpacity onPress={onPress} activeOpacity={0.8}>
        <CardContent />
      </TouchableOpacity>
    );
  }

  return <CardContent />;
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    padding: SIZES.SPACING_MD,
    marginBottom: SIZES.SPACING_MD,
    borderWidth: 1,
    borderColor: COLORS.TEXT_DISABLED,
    shadowColor: COLORS.TEXT_PRIMARY,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  title: {
    fontSize: SIZES.FONT_SIZE_LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SIZES.SPACING_MD,
    textAlign: 'center',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statItem: {
    width: '48%',
    backgroundColor: COLORS.BACKGROUND,
    borderRadius: SIZES.BORDER_RADIUS_SM,
    padding: SIZES.SPACING_SM,
    marginBottom: SIZES.SPACING_SM,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: `${COLORS.PRIMARY}20`,
  },
  statHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: SIZES.SPACING_XS,
  },
  statIcon: {
    fontSize: SIZES.FONT_SIZE_LG,
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trendIcon: {
    fontSize: SIZES.FONT_SIZE_SM,
    marginRight: 2,
  },
  trendValue: {
    fontSize: SIZES.FONT_SIZE_XS,
    fontWeight: '600',
  },
  statValue: {
    fontSize: SIZES.FONT_SIZE_XL,
    fontWeight: 'bold',
    marginBottom: SIZES.SPACING_XS,
    textAlign: 'center',
  },
  statLabel: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    fontWeight: '500',
  },
});

export default RealTimeStatsCard;
