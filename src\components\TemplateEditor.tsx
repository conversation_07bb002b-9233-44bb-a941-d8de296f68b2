import React, { useRef, useState, useEffect } from 'react';
import { Template, TemplateElement } from '../types';
import { useTemplateStore } from '../store';
import { ToolsPanel } from './editor/ToolsPanel';
import { Canvas } from './editor/Canvas';
import { InfoPanel } from './editor/InfoPanel';
import { ElementPropertiesPanel } from './editor/ElementPropertiesPanel';
import { useEditorState } from './editor/useEditorState';
import { handleMouseDown, handleMouseMove, handleMouseUp } from './editor/elementOperations';

export const TemplateEditor: React.FC = () => {
  const { selectedTemplate, updateTemplate } = useTemplateStore();
  const canvasRef = useRef<HTMLDivElement>(null);
  const [tempElement, setTempElement] = useState<TemplateElement | null>(null);

  // 添加一個簡單的日誌，幫助診斷問題
  console.log("TemplateEditor 渲染中, selectedTemplate ID:", selectedTemplate?.id || 'null');
  console.log("TemplateEditor 渲染中, selectedTemplate 完整數據:", selectedTemplate ?
    {
      id: selectedTemplate.id,
      name: selectedTemplate.name,
      type: selectedTemplate.type,
      elementsCount: selectedTemplate.elements?.length || 0
    } : 'null');

  // 如果沒有選中的模板，顯示空狀態
  if (!selectedTemplate) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-100">
        <div className="text-center">
          <h3 className="text-xl font-medium text-gray-700 mb-2">沒有選中的模板</h3>
          <p className="text-gray-500">請從側邊欄選擇一個模板進行編輯</p>
        </div>
      </div>
    );
  }

  // 確保模板有 ID
  if (!selectedTemplate.id) {
    console.error("警告: 選中的模板沒有ID!");
  }

  try {
    // 使用自定義 hook 管理編輯器狀態
    const {
      zoom,
      setZoom,
      templateName,
      setTemplateName,
      expandedSections,
      toggleSection,
      selectedElementId,
      setSelectedElementId,
      // 多選功能 - 獲取新增的狀態和函數
      selectedElementIds,
      setSelectedElementIds,
      isMultiSelectMode,
      setIsMultiSelectMode,
      selectionBox,
      setSelectionBox,
      selectElementsByBox,
      toggleElementSelection,
      clearSelection,
      alignSelectedElements,
      distributeSelectedElements,
      moveSelectedElements,
      isDrawing,
      setIsDrawing,
      startPoint,
      setStartPoint,
      selectedTool,
      setSelectedTool,
      localElements,
      setLocalElements,
      elementProperties,
      setElementProperties,
      addElement,
      isMultiMoving, // 添加這行以解構 isMultiMoving 屬性
      // 添加複製貼上功能相關函數
      handleCopy,
      handlePaste,
      // 添加顯示元素 ID 功能相關屬性
      showElementId,
      toggleElementIdDisplay
    } = useEditorState(selectedTemplate);

    // 獲取當前選中的元素
    const selectedElement = localElements.find(element => element.id === selectedElementId) || null;

    // 更新選中元素的屬性
    const updateSelectedElement = (updates: Partial<TemplateElement>) => {
      if (!selectedElementId) return;

      const updatedElements = localElements.map(element =>
        element.id === selectedElementId ? { ...element, ...updates } : element
      );

      setLocalElements(updatedElements);

      // 更新全局模板狀態
      updateTemplate({
        ...selectedTemplate,
        elements: updatedElements
      });
    };

    // 更新任意元素的屬性
    const updateElement = (id: string, updates: Partial<TemplateElement>) => {
      const updatedElements = localElements.map(element =>
        element.id === id ? { ...element, ...updates } : element
      );

      setLocalElements(updatedElements);
    };

    // 刪除選中的元素
    const deleteSelectedElement = () => {
      // 多選功能 - 支持刪除多個選中元素
      if (selectedElementIds.length > 0) {
        const updatedElements = localElements.filter(element =>
          !selectedElementIds.includes(element.id)
        );

        setLocalElements(updatedElements);
        setSelectedElementId(null);
        setSelectedElementIds([]);

        // 更新全局模板狀態
        updateTemplate({
          ...selectedTemplate,
          elements: updatedElements
        });
      } else if (selectedElementId) {
        // 傳統的單選刪除邏輯
        const updatedElements = localElements.filter(element =>
          element.id !== selectedElementId
        );

        setLocalElements(updatedElements);
        setSelectedElementId(null);

        // 更新全局模板狀態
        updateTemplate({
          ...selectedTemplate,
          elements: updatedElements
        });
      }
    };

    // 圖層順序變化處理 - 新增此函數
    const handleLayerOrderChange = (newElements: TemplateElement[]) => {
      console.log('更新圖層順序:', newElements.map(el => el.id));

      setLocalElements(newElements);

      // 更新全局模板狀態
      updateTemplate({
        ...selectedTemplate,
        elements: newElements
      });
    };

    // 選擇工具事件處理
    const selectStaticTool = (tool: string) => {
      setSelectedTool(tool === selectedTool ? null : tool);

      // 多選功能 - 清除當前選擇
      if (tool && (selectedElementId || selectedElementIds.length > 0)) {
        setSelectedElementId(null);
        setSelectedElementIds([]);
      }
    };

    // 畫布鼠標事件處理
    const handleCanvasMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
      // 如果點擊了現有元素，不進行繪製
      if ((e.target as HTMLElement).closest('[data-element-id]')) {
        console.log('點擊了現有元素，不進行繪製');
        return;
      }

      console.log('TemplateEditor - MouseDown - canvasRef 存在:', !!canvasRef.current);
      handleMouseDown(e, canvasRef, selectedTool, setIsDrawing, setStartPoint, zoom);
    };

    const handleCanvasMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
      handleMouseMove(
        e,
        canvasRef,
        isDrawing,
        selectedTool,
        startPoint,
        zoom,
        tempElement,
        setTempElement
      );
    };

    const handleCanvasMouseUp = (e: React.MouseEvent<HTMLDivElement>) => {
      console.log('TemplateEditor - MouseUp - canvasRef 存在:', !!canvasRef.current);
      handleMouseUp(
        e,
        canvasRef,
        isDrawing,
        selectedTool,
        startPoint,
        zoom,
        tempElement,
        setIsDrawing,
        setTempElement,
        addElement
      );
    };

    // 鍵盤事件處理 - 刪除元素和取消選擇
    useEffect(() => {
      console.log('TemplateEditor: 設置鍵盤事件監聽器');

      // 檢查相關函數是否存在
      console.log('TemplateEditor: handleCopy 是否存在:', typeof handleCopy === 'function');
      console.log('TemplateEditor: handlePaste 是否存在:', typeof handlePaste === 'function');
      console.log('TemplateEditor: deleteSelectedElement 是否存在:', typeof deleteSelectedElement === 'function');
      console.log('TemplateEditor: clearSelection 是否存在:', typeof clearSelection === 'function');

      const handleKeyDown = (e: KeyboardEvent) => {
        // 複製貼上操作已由 Canvas 組件處理
        // 這裡只處理刪除和 ESC 取消選擇
        if (e.key === 'Delete') {
          console.log('TemplateEditor: 檢測到 Delete 按下，刪除選中元素');
          if (typeof deleteSelectedElement === 'function') {
            deleteSelectedElement();
          } else {
            console.error('TemplateEditor: deleteSelectedElement 不是一個函數!');
          }
        } else if (e.key === 'Escape') {
          // 多選功能 - 按ESC取消選擇
          console.log('TemplateEditor: 檢測到 Escape 按下，取消選擇');
          if (typeof clearSelection === 'function') {
            clearSelection();
          } else {
            console.error('TemplateEditor: clearSelection 不是一個函數!');
          }
        }
        // 手動測試複製貼上功能
        // 使用 e.key.toLowerCase() 來忽略 Caps Lock 狀態
        else if (e.ctrlKey && e.key.toLowerCase() === 'c') {
          console.log('TemplateEditor: 檢測到 Ctrl+C 按下，已由 Canvas 處理');
          console.log('TemplateEditor: 按鍵名稱:', e.key);
        } else if (e.ctrlKey && e.key.toLowerCase() === 'v') {
          console.log('TemplateEditor: 檢測到 Ctrl+V 按下，已由 Canvas 處理');
          console.log('TemplateEditor: 按鍵名稱:', e.key);
        }
      };

      window.addEventListener('keydown', handleKeyDown);
      return () => {
        console.log('TemplateEditor: 移除鍵盤事件監聽器');
        window.removeEventListener('keydown', handleKeyDown);
      };
    }, [selectedElementId, selectedElementIds, localElements, deleteSelectedElement, clearSelection, handleCopy, handlePaste]);

    return (
      <div className="flex h-full w-full overflow-hidden">        {/* 工具面板 */}        <ToolsPanel
          expandedSections={expandedSections}
          toggleSection={toggleSection}
          selectStaticTool={selectStaticTool}
          selectedTool={selectedTool}
          localElements={localElements}
          showElementId={showElementId}
          toggleElementIdDisplay={toggleElementIdDisplay}
          onLayerOrderChange={handleLayerOrderChange}
          setSelectedElementId={setSelectedElementId}
          setSelectedElementIds={setSelectedElementIds}
          selectedElementId={selectedElementId}
        />

        {/* 畫布區域 */}        <Canvas
          canvasRef={canvasRef}
          selectedTemplate={selectedTemplate}
          zoom={zoom}
          setZoom={setZoom}
          templateName={templateName}
          setTemplateName={setTemplateName}
          localElements={localElements}
          setLocalElements={setLocalElements}
          selectedElementId={selectedElementId}
          setSelectedElementId={setSelectedElementId}
          // 多選功能 - 傳遞多選相關屬性
          selectedElementIds={selectedElementIds}
          setSelectedElementIds={setSelectedElementIds}
          isMultiSelectMode={isMultiSelectMode}
          setIsMultiSelectMode={setIsMultiSelectMode}
          selectionBox={selectionBox}
          setSelectionBox={setSelectionBox}
          selectElementsByBox={selectElementsByBox}
          toggleElementSelection={toggleElementSelection}
          clearSelection={clearSelection}
          moveSelectedElements={moveSelectedElements}
          isMultiMoving={isMultiMoving} // 傳遞多選移動狀態
          isDrawing={isDrawing}
          setIsDrawing={setIsDrawing}
          startPoint={startPoint}
          setStartPoint={setStartPoint}
          selectedTool={selectedTool}
          setSelectedTool={setSelectedTool}
          handleCanvasMouseDown={handleCanvasMouseDown}
          handleCanvasMouseMove={handleCanvasMouseMove}
          handleCanvasMouseUp={handleCanvasMouseUp}
          updateTemplate={updateTemplate}
          updateElement={updateElement}
          tempElement={tempElement}
          // 複製貼上功能
          handleCopy={handleCopy}
          handlePaste={handlePaste}
        />

        {/* 右側面板 */}
        <div className="w-64 flex flex-col">
          {/* 信息面板 */}
          <InfoPanel
            selectedTemplate={selectedTemplate}
            zoom={zoom}
            setZoom={setZoom}
          />

          {/* 元素屬性面板 */}
          <div className="flex-1 overflow-auto">
            <ElementPropertiesPanel
              selectedElement={selectedElement}
              elementProperties={elementProperties}
              setElementProperties={setElementProperties}
              updateSelectedElement={updateSelectedElement}
              // 多選功能 - 傳遞多選相關屬性
              selectedElementIds={selectedElementIds}
              alignSelectedElements={alignSelectedElements}
              distributeSelectedElements={distributeSelectedElements}
              // 傳遞模板的顏色類型
              colorType={selectedTemplate.color}
            />
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error("TemplateEditor 出錯:", error);
    return (
      <div className="flex items-center justify-center h-full bg-gray-100">
        <div className="text-center">
          <h3 className="text-xl font-medium text-red-700 mb-2">出錯了</h3>
          <p className="text-gray-500">編輯器載入時發生錯誤: {String(error)}</p>
        </div>
      </div>
    );
  }
};