// EPD Manager App - 設備狀態管理

import { create } from 'zustand';
import { Device, DeviceState, DeviceConfig } from '../types';
import { webSocketService, frontendWebSocketClient, subscribeToDeviceStatus } from '../services/WebSocketService';
import { apiService } from '../services/ApiService';
import { generateRandomMac, generateDeviceSize, generateColorType } from '../utils/generators';

interface DeviceStore extends DeviceState {
  // Actions
  fetchDevices: (storeId: string) => Promise<void>;
  addCustomDevice: (deviceConfig: DeviceConfig) => boolean;
  removeCustomDevice: (index: number) => boolean;
  requestDeviceImage: (macAddress: string) => void;
  refreshDeviceList: (storeId?: string) => Promise<void>;
  syncDevices: (storeId?: string) => Promise<boolean>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;

  // WebSocket 相關
  connectWebSocket: () => Promise<boolean>;
  disconnectWebSocket: () => void;
  subscribeToRealTimeUpdates: (storeId: string) => () => void;
  isWebSocketConnected: () => boolean;

  // Getters
  getAllDevices: () => Device[];
  getCustomDevices: () => Device[];
  getDeviceByMac: (macAddress: string) => Device | undefined;
  getDeviceCount: () => number;
  getCustomDeviceCount: () => number;
}

export const useDeviceStore = create<DeviceStore>()((set, get) => ({
  // Initial state
  devices: [],
  customDevices: [],
  loading: false,
  error: null,

  // Actions
  fetchDevices: async (storeId: string) => {
    try {
      set({ loading: true, error: null });

      const result = await apiService.getDevices(storeId);

      if (result.success && result.data) {
        set({
          devices: result.data,
          loading: false,
          error: null
        });
        console.log(`成功獲取 ${result.data.length} 個設備`);
      } else {
        set({
          loading: false,
          error: result.error || '獲取設備列表失敗'
        });
      }
    } catch (error: any) {
      set({
        loading: false,
        error: error.message || '獲取設備列表失敗'
      });
      console.error('獲取設備列表失敗:', error);
    }
  },

  addCustomDevice: (deviceConfig: DeviceConfig) => {
    try {
      // 驗證 MAC 地址格式
      if (!deviceConfig.macAddress) {
        set({ error: 'MAC 地址不能為空' });
        return false;
      }

      // 檢查 MAC 地址是否已存在
      const { customDevices } = get();
      const existingDevice = customDevices.find(device => device.macAddress === deviceConfig.macAddress);
      if (existingDevice) {
        set({ error: 'MAC 地址已存在' });
        return false;
      }

      // 添加設備到 WebSocket 服務
      webSocketService.addCustomDevice(deviceConfig);

      // 更新本地狀態
      const newDevice: Device = {
        macAddress: deviceConfig.macAddress,
        status: deviceConfig.status || 'online',
        data: {
          size: deviceConfig.size || generateDeviceSize(),
          battery: Math.floor(Math.random() * 101),
          rssi: -1 * Math.floor(Math.random() * 101),
          colorType: deviceConfig.colorType || generateColorType(),
          imageCode: deviceConfig.imageCode
        }
      };

      const updatedCustomDevices = [...customDevices, newDevice];
      set({ 
        customDevices: updatedCustomDevices,
        error: null 
      });

      console.log('添加自定義設備成功:', deviceConfig.macAddress);
      return true;
    } catch (error: any) {
      set({ error: error.message || '添加設備失敗' });
      return false;
    }
  },

  removeCustomDevice: (index: number) => {
    try {
      const { customDevices } = get();
      
      if (index < 0 || index >= customDevices.length) {
        set({ error: '無效的設備索引' });
        return false;
      }

      // 從 WebSocket 服務移除設備
      const success = webSocketService.removeCustomDevice(index);
      
      if (success) {
        // 更新本地狀態
        const updatedCustomDevices = customDevices.filter((_, i) => i !== index);
        set({ 
          customDevices: updatedCustomDevices,
          error: null 
        });

        console.log('移除自定義設備成功');
        return true;
      } else {
        set({ error: '移除設備失敗' });
        return false;
      }
    } catch (error: any) {
      set({ error: error.message || '移除設備失敗' });
      return false;
    }
  },

  requestDeviceImage: (macAddress: string) => {
    try {
      webSocketService.requestDeviceImage(macAddress);
      console.log(`請求設備 ${macAddress} 的預覽圖像`);
    } catch (error: any) {
      set({ error: error.message || '請求圖像失敗' });
    }
  },

  refreshDeviceList: async (storeId?: string) => {
    if (storeId) {
      // 如果提供了storeId，從API獲取真實數據
      await get().fetchDevices(storeId);
    } else {
      // 如果沒有提供storeId，保持原有的WebSocket邏輯（用於向後兼容）
      try {
        const allDevices = webSocketService.getDeviceList();

        const customDevices = get().customDevices;
        const defaultDeviceCount = allDevices.length - customDevices.length;
        const defaultDevices = allDevices.slice(0, defaultDeviceCount);

        set({
          devices: defaultDevices,
          error: null
        });

        console.log('刷新設備列表成功（WebSocket模式）');
      } catch (error: any) {
        set({ error: error.message || '刷新設備列表失敗' });
      }
    }
  },

  syncDevices: async (storeId?: string) => {
    try {
      set({ loading: true, error: null });

      const result = await apiService.syncDevices(storeId);

      if (result.success) {
        // 同步成功後重新獲取設備列表
        if (storeId) {
          await get().fetchDevices(storeId);
        }
        set({ loading: false });
        console.log('設備同步成功');
        return true;
      } else {
        set({
          loading: false,
          error: result.error || '設備同步失敗'
        });
        return false;
      }
    } catch (error: any) {
      set({
        loading: false,
        error: error.message || '設備同步失敗'
      });
      console.error('設備同步失敗:', error);
      return false;
    }
  },

  clearError: () => {
    set({ error: null });
  },

  setLoading: (loading: boolean) => {
    set({ loading });
  },

  setError: (error: string | null) => {
    set({ error });
  },

  // WebSocket 相關方法
  connectWebSocket: async () => {
    try {
      const connected = await frontendWebSocketClient.connect();
      if (connected) {
        console.log('前端 WebSocket 連接成功');
        return true;
      } else {
        console.error('前端 WebSocket 連接失敗');
        set({ error: '無法連接到即時更新服務' });
        return false;
      }
    } catch (error: any) {
      console.error('連接前端 WebSocket 失敗:', error);
      set({ error: error.message || '連接即時更新服務失敗' });
      return false;
    }
  },

  disconnectWebSocket: () => {
    frontendWebSocketClient.disconnect();
    console.log('前端 WebSocket 已斷開');
  },

  subscribeToRealTimeUpdates: (storeId: string) => {
    console.log(`訂閱門店 ${storeId} 的即時設備更新`);

    const handleDeviceUpdate = (event: any) => {
      console.log('收到設備狀態更新事件:', event);

      if (event.devices && Array.isArray(event.devices)) {
        // 更新設備列表
        set(state => {
          const updatedDevices = [...state.devices];

          event.devices.forEach((updatedDevice: Device) => {
            const index = updatedDevices.findIndex(d => d._id === updatedDevice._id);
            if (index >= 0) {
              // 更新現有設備
              updatedDevices[index] = {
                ...updatedDevices[index],
                ...updatedDevice,
                lastSeen: updatedDevice.lastSeen ? new Date(updatedDevice.lastSeen) : null,
                updatedAt: updatedDevice.updatedAt ? new Date(updatedDevice.updatedAt) : null,
              };
            } else if (event.updateType === 'create') {
              // 添加新設備
              updatedDevices.push({
                ...updatedDevice,
                lastSeen: updatedDevice.lastSeen ? new Date(updatedDevice.lastSeen) : null,
                createdAt: updatedDevice.createdAt ? new Date(updatedDevice.createdAt) : null,
                updatedAt: updatedDevice.updatedAt ? new Date(updatedDevice.updatedAt) : null,
              });
            }
          });

          return { devices: updatedDevices };
        });

        console.log(`已更新 ${event.devices.length} 個設備的狀態`);
      }
    };

    return subscribeToDeviceStatus(storeId, handleDeviceUpdate, {
      includeImageStatus: true,
      includeBatteryInfo: true
    });
  },

  isWebSocketConnected: () => {
    return frontendWebSocketClient.getConnectionStatus();
  },

  // Getters
  getAllDevices: () => {
    const { devices, customDevices } = get();
    return [...devices, ...customDevices];
  },

  getCustomDevices: () => {
    const { customDevices } = get();
    return customDevices;
  },

  getDeviceByMac: (macAddress: string) => {
    const { devices, customDevices } = get();
    const allDevices = [...devices, ...customDevices];
    return allDevices.find(device => device.macAddress === macAddress);
  },

  getDeviceCount: () => {
    const { devices, customDevices } = get();
    return devices.length + customDevices.length;
  },

  getCustomDeviceCount: () => {
    const { customDevices } = get();
    return customDevices.length;
  }
}));

// 導出便捷的 hooks
export const useDevices = () => {
  const store = useDeviceStore();
  return {
    // State
    devices: store.devices,
    customDevices: store.customDevices,
    loading: store.loading,
    error: store.error,

    // Actions
    fetchDevices: store.fetchDevices,
    addCustomDevice: store.addCustomDevice,
    removeCustomDevice: store.removeCustomDevice,
    requestDeviceImage: store.requestDeviceImage,
    refreshDeviceList: store.refreshDeviceList,
    syncDevices: store.syncDevices,
    clearError: store.clearError,

    // WebSocket 相關
    connectWebSocket: store.connectWebSocket,
    disconnectWebSocket: store.disconnectWebSocket,
    subscribeToRealTimeUpdates: store.subscribeToRealTimeUpdates,
    isWebSocketConnected: store.isWebSocketConnected,

    // Getters
    getAllDevices: store.getAllDevices(),
    getCustomDevices: store.getCustomDevices(),
    getDeviceByMac: store.getDeviceByMac,
    getDeviceCount: store.getDeviceCount(),
    getCustomDeviceCount: store.getCustomDeviceCount(),
  };
};

export const useDeviceActions = () => {
  const store = useDeviceStore();
  return {
    addCustomDevice: store.addCustomDevice,
    removeCustomDevice: store.removeCustomDevice,
    requestDeviceImage: store.requestDeviceImage,
    refreshDeviceList: store.refreshDeviceList,
    clearError: store.clearError,
    setLoading: store.setLoading,
    setError: store.setError,
  };
};

export const useDeviceState = () => {
  const store = useDeviceStore();
  return {
    devices: store.devices,
    customDevices: store.customDevices,
    loading: store.loading,
    error: store.error,
    getAllDevices: store.getAllDevices(),
    getDeviceCount: store.getDeviceCount(),
    getCustomDeviceCount: store.getCustomDeviceCount(),
  };
};

// 快速添加設備的便捷函數
export const useQuickAddDevice = () => {
  const { addCustomDevice } = useDeviceActions();
  
  return {
    addRandomDevice: () => {
      const deviceConfig: DeviceConfig = {
        macAddress: generateRandomMac(),
        status: 'online',
        size: generateDeviceSize(),
        colorType: generateColorType()
      };
      return addCustomDevice(deviceConfig);
    },
    
    addDeviceWithMac: (macAddress: string) => {
      const deviceConfig: DeviceConfig = {
        macAddress,
        status: 'online',
        size: generateDeviceSize(),
        colorType: generateColorType()
      };
      return addCustomDevice(deviceConfig);
    }
  };
};
