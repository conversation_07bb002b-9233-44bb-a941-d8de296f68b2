const { execSync } = require('child_process');
const path = require('path');

console.log('Starting Android build with detailed logs...');

try {
  // 切換到 android 目錄
  const androidDir = path.resolve(__dirname, '../android');
  
  // 使用 --info 和 --stacktrace 獲取詳細日誌
  const cmd = 'gradlew.bat app:assembleDebug --info --stacktrace -x lint -x test';
  
  console.log(`Executing: ${cmd} in directory: ${androidDir}`);
  
  // 執行命令
  execSync(cmd, { 
    cwd: androidDir,
    stdio: 'inherit' // 將輸出直接顯示在控制台
  });
  
  console.log('Build completed successfully!');
} catch (error) {
  console.error('Build failed with error:', error.message);
  process.exit(1);
}
