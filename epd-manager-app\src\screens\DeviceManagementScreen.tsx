// EPD Manager App - 設備管理頁面

import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useDevices } from '../stores/deviceStore';
import { useStores } from '../stores/storeStore';
import { apiService } from '../services/ApiService';
import { COLORS, SIZES } from '../utils/constants';
import { FilterTabs, FilterOption } from '../components/FilterTabs';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SIZES.SPACING_MD,
    backgroundColor: COLORS.SURFACE,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.TEXT_DISABLED,
  },
  title: {
    fontSize: SIZES.FONT_SIZE_XL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },

  errorContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: COLORS.ERROR,
    padding: SIZES.SPACING_SM,
    margin: SIZES.SPACING_MD,
    borderRadius: SIZES.BORDER_RADIUS_SM,
  },
  errorText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_SM,
    flex: 1,
  },
  errorDismiss: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_MD,
    fontWeight: 'bold',
    padding: SIZES.SPACING_XS,
  },
  scrollView: {
    flex: 1,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: SIZES.SPACING_MD,
    backgroundColor: COLORS.SURFACE,
    margin: SIZES.SPACING_MD,
    borderRadius: SIZES.BORDER_RADIUS_MD,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: SIZES.FONT_SIZE_XL,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
  },
  statLabel: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
    marginTop: SIZES.SPACING_XS,
  },
  sectionTitle: {
    fontSize: SIZES.FONT_SIZE_LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginHorizontal: SIZES.SPACING_MD,
    marginBottom: SIZES.SPACING_SM,
  },
  deviceCard: {
    backgroundColor: COLORS.SURFACE,
    margin: SIZES.SPACING_MD,
    marginTop: SIZES.SPACING_SM,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    padding: SIZES.SPACING_MD,
    position: 'relative',
  },
  deviceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SIZES.SPACING_SM,
  },
  deviceInfo: {
    flex: 1,
  },
  deviceMac: {
    fontSize: SIZES.FONT_SIZE_MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    fontFamily: 'monospace',
    marginBottom: SIZES.SPACING_XS,
  },
  deviceDetails: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
  },
  statusBadge: {
    paddingHorizontal: SIZES.SPACING_SM,
    paddingVertical: SIZES.SPACING_XS,
    borderRadius: SIZES.BORDER_RADIUS_SM,
  },
  statusOnline: {
    backgroundColor: COLORS.SUCCESS,
  },
  statusOffline: {
    backgroundColor: COLORS.TEXT_DISABLED,
  },
  statusText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_XS,
    fontWeight: '600',
  },
  deviceActions: {
    flexDirection: 'row',
    gap: SIZES.SPACING_SM,
  },
  actionButton: {
    backgroundColor: COLORS.INFO,
    paddingHorizontal: SIZES.SPACING_MD,
    paddingVertical: SIZES.SPACING_SM,
    borderRadius: SIZES.BORDER_RADIUS_SM,
  },
  actionButtonText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_SM,
    fontWeight: '600',
  },
  deleteButton: {
    backgroundColor: COLORS.ERROR,
  },
  deleteButtonText: {
    color: COLORS.SURFACE,
  },
  customBadge: {
    position: 'absolute',
    top: SIZES.SPACING_SM,
    right: SIZES.SPACING_SM,
    backgroundColor: COLORS.WARNING,
    paddingHorizontal: SIZES.SPACING_SM,
    paddingVertical: SIZES.SPACING_XS,
    borderRadius: SIZES.BORDER_RADIUS_SM,
  },
  customBadgeText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_XS,
    fontWeight: '600',
  },
  emptyContainer: {
    alignItems: 'center',
    padding: SIZES.SPACING_XL,
  },
  emptyText: {
    fontSize: SIZES.FONT_SIZE_LG,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SIZES.SPACING_SM,
  },
  emptySubtext: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_DISABLED,
  },
  filterContainer: {
    marginBottom: SIZES.SPACING_MD,
    marginHorizontal: -SIZES.SPACING_MD,
  },
});

export const DeviceManagementScreen: React.FC = () => {
  const [refreshing, setRefreshing] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [deviceStats, setDeviceStats] = useState({
    online: 0,
    offline: 0,
    pendingUpdate: 0,
    updated: 0
  });
  const { selectedStore } = useStores();
  const {
    devices,
    customDevices,
    error,
    fetchDevices,
    removeCustomDevice,
    refreshDeviceList,
    syncDevices,
    clearError,
    connectWebSocket,
    disconnectWebSocket,
    subscribeToRealTimeUpdates,
    isWebSocketConnected
  } = useDevices();

  // 計算過濾後的設備列表
  const filteredDevices = useMemo(() => {
    const allDevices = [...devices, ...customDevices];
    switch (selectedFilter) {
      case 'online':
        return allDevices.filter(device => device.status === 'online');
      case 'offline':
        return allDevices.filter(device => device.status === 'offline');
      default:
        return allDevices;
    }
  }, [devices, customDevices, selectedFilter]);

  // 計算過濾選項
  const filterOptions = useMemo((): FilterOption[] => {
    const allDevices = [...devices, ...customDevices];
    const onlineCount = allDevices.filter(d => d.status === 'online').length;
    const offlineCount = allDevices.filter(d => d.status === 'offline').length;

    return [
      { key: 'all', label: '全部', count: allDevices.length },
      { key: 'online', label: '在線', count: onlineCount },
      { key: 'offline', label: '離線', count: offlineCount },
    ];
  }, [devices, customDevices]);

  // 計算設備統計信息
  const calculateDeviceStats = (deviceList: any[]) => {
    const online = deviceList.filter(d => d.status === 'online').length;
    const offline = deviceList.filter(d => d.status === 'offline').length;
    const updated = deviceList.filter(d => d.imageUpdateStatus === '已更新').length;
    const pendingUpdate = deviceList.filter(d => d.imageUpdateStatus === '未更新' || !d.imageUpdateStatus).length;

    setDeviceStats({
      online,
      offline,
      pendingUpdate,
      updated
    });
  };

  useEffect(() => {
    // 頁面加載時獲取真實設備數據
    if (selectedStore) {
      loadDevices();

      // 連接 WebSocket 並訂閱即時更新
      const initializeWebSocket = async () => {
        try {
          // 如果 WebSocket 未連接，先連接
          if (!isWebSocketConnected()) {
            console.log('連接前端 WebSocket...');
            const connected = await connectWebSocket();
            if (!connected) {
              console.error('WebSocket 連接失敗，將使用定時刷新模式');
              return;
            }
          }

          // 訂閱當前門店的設備狀態更新
          const unsubscribe = subscribeToRealTimeUpdates(selectedStore.id);
          console.log(`已訂閱門店 ${selectedStore.id} 的即時設備更新`);

          // 返回清理函數
          return unsubscribe;
        } catch (error) {
          console.error('初始化 WebSocket 失敗:', error);
        }
      };

      let unsubscribe: (() => void) | undefined;

      initializeWebSocket().then(cleanup => {
        unsubscribe = cleanup;
      });

      // 清理函數
      return () => {
        if (unsubscribe) {
          unsubscribe();
          console.log(`已取消訂閱門店 ${selectedStore.id} 的即時設備更新`);
        }
      };
    }
  }, [selectedStore]);

  // 當設備列表變化時重新計算統計信息
  useEffect(() => {
    const allDevices = [...devices, ...customDevices];
    calculateDeviceStats(allDevices);
  }, [devices, customDevices]);

  // 監聽設備列表變化，當設備數據更新時自動重新計算統計信息
  // 在 React Native 中，我們通過監聽 store 中的設備列表變化來實現即時更新
  useEffect(() => {
    // 當設備列表發生變化時，會自動觸發統計信息的重新計算
    // 這個 useEffect 會在 devices 或 customDevices 變化時執行
    console.log('設備列表已更新，重新計算統計信息');
  }, [devices, customDevices]);

  // 定時刷新設備統計信息（僅在 WebSocket 未連接時使用）
  useEffect(() => {
    if (!selectedStore) return;

    // 檢查 WebSocket 連接狀態，如果已連接則不需要定時刷新
    if (isWebSocketConnected()) {
      console.log('WebSocket 已連接，跳過定時刷新');
      return;
    }

    console.log('WebSocket 未連接，啟用定時刷新模式');

    // 設置定時器，每60秒刷新一次設備統計
    const statsInterval = setInterval(() => {
      // 再次檢查 WebSocket 狀態，如果已連接則停止定時刷新
      if (isWebSocketConnected()) {
        console.log('WebSocket 已連接，停止定時刷新');
        clearInterval(statsInterval);
        return;
      }

      console.log('定時刷新設備統計信息');
      loadDevices();
    }, 60000); // 60秒間隔，避免過於頻繁的請求

    // 清理函數
    return () => {
      clearInterval(statsInterval);
    };
  }, [selectedStore, isWebSocketConnected]);

  // 組件卸載時清理 WebSocket 連接
  useEffect(() => {
    return () => {
      console.log('DeviceManagementScreen 卸載，清理 WebSocket 連接');
      // 注意：這裡不直接斷開 WebSocket，因為其他組件可能還在使用
      // WebSocket 連接由全局管理，只在應用退出時斷開
    };
  }, []);

  const loadDevices = async () => {
    if (!selectedStore) return;

    // 參考 test-ws-client-interactive.js 的做法，使用 id 作為 storeId
    const storeId = selectedStore.id;
    if (!storeId) {
      console.error('門店 ID 不存在，請重新選擇門店');
      return;
    }

    try {
      await fetchDevices(storeId);
    } catch (error) {
      console.error('載入設備列表失敗:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);

    if (selectedStore) {
      // 參考 test-ws-client-interactive.js 的做法，使用 id 作為 storeId
      const storeId = selectedStore.id;
      if (storeId) {
        // 先同步設備狀態，然後重新獲取設備列表
        await syncDevices(storeId);
        await loadDevices();
      } else {
        console.error('門店 ID 不存在，無法同步設備');
      }
    } else {
      // 如果沒有選擇門店，使用原有的WebSocket模式
      await refreshDeviceList();
    }

    setRefreshing(false);
  };

  const handleRemoveDevice = (index: number, device: any) => {
    Alert.alert(
      '確認刪除',
      `確定要刪除設備 ${device.macAddress} 嗎？`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '刪除',
          style: 'destructive',
          onPress: () => {
            const success = removeCustomDevice(index);
            if (success) {
              Alert.alert('成功', '設備已刪除');
            }
          }
        }
      ]
    );
  };

  const handleSendPreview = async (device: any) => {
    console.log('準備發送預覽圖，設備信息:', device);

    if (!device._id) {
      console.error('設備缺少_id屬性:', device);
      Alert.alert('錯誤', `設備ID不存在，無法發送。設備MAC: ${device.macAddress}`);
      return;
    }

    if (!selectedStore) {
      Alert.alert('錯誤', '請先選擇門店');
      return;
    }

    try {
      console.log(`開始發送預覽圖到設備 ${device.macAddress} (ID: ${device._id})`);
      Alert.alert('提示', `正在發送預覽圖到設備 ${device.macAddress}...`);

      const result = await apiService.sendDevicePreviewToGateway(device._id, {
        sendToAllGateways: false,
        storeId: selectedStore.id
      });

      console.log('發送預覽圖結果:', result);

      if (result.success) {
        Alert.alert('成功', `設備 ${device.macAddress} 預覽圖發送成功`);
        // 發送成功後重新載入設備列表以更新狀態
        await loadDevices();
      } else {
        Alert.alert('失敗', `設備 ${device.macAddress} 預覽圖發送失敗: ${result.error || '未知錯誤'}`);
      }
    } catch (error: any) {
      console.error('發送預覽圖失敗:', error);
      Alert.alert('錯誤', `發送預覽圖失敗: ${error.message || '未知錯誤'}`);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>設備管理</Text>
      </View>

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity onPress={clearError}>
            <Text style={styles.errorDismiss}>✕</Text>
          </TouchableOpacity>
        </View>
      )}

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[COLORS.PRIMARY]}
          />
        }
      >
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: COLORS.SUCCESS }]}>{deviceStats.online}</Text>
            <Text style={styles.statLabel}>在線設備</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: COLORS.TEXT_DISABLED }]}>{deviceStats.offline}</Text>
            <Text style={styles.statLabel}>離線設備</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: COLORS.WARNING }]}>{deviceStats.pendingUpdate}</Text>
            <Text style={styles.statLabel}>待更新圖片</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: COLORS.INFO }]}>{deviceStats.updated}</Text>
            <Text style={styles.statLabel}>已更新圖片</Text>
          </View>
        </View>

        {/* 過濾標籤 */}
        {(devices.length > 0 || customDevices.length > 0) && (
          <FilterTabs
            options={filterOptions}
            selectedFilter={selectedFilter}
            onFilterChange={setSelectedFilter}
            style={styles.filterContainer}
          />
        )}

        <Text style={styles.sectionTitle}>
          設備列表 ({filteredDevices.length})
        </Text>

        {filteredDevices.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              {devices.length === 0 && customDevices.length === 0
                ? '暫無設備'
                : '沒有符合條件的設備'
              }
            </Text>
            <Text style={styles.emptySubtext}>
              {devices.length === 0 && customDevices.length === 0
                ? '請等待設備連接或下拉刷新'
                : '請嘗試其他過濾條件'
              }
            </Text>
          </View>
        ) : (
          filteredDevices.map((device, index) => {
          const isCustomDevice = index >= devices.length;
          const customIndex = isCustomDevice ? index - devices.length : -1;

          return (
            <View key={device.macAddress} style={styles.deviceCard}>
              <View style={styles.deviceHeader}>
                <View style={styles.deviceInfo}>
                  <Text style={styles.deviceMac}>{device.macAddress}</Text>
                  <Text style={styles.deviceDetails}>
                    {device.data.size} | {device.data.colorType || 'BW'} | 
                    電量: {device.data.battery}% | 
                    信號: {device.data.rssi}dBm
                  </Text>
                </View>
                <View style={[
                  styles.statusBadge,
                  device.status === 'online' ? styles.statusOnline : styles.statusOffline
                ]}>
                  <Text style={styles.statusText}>
                    {device.status === 'online' ? '在線' : '離線'}
                  </Text>
                </View>
              </View>

              <View style={styles.deviceActions}>
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() => handleSendPreview(device)}
                >
                  <Text style={styles.actionButtonText}>發送</Text>
                </TouchableOpacity>

                {isCustomDevice && (
                  <TouchableOpacity
                    style={[styles.actionButton, styles.deleteButton]}
                    onPress={() => handleRemoveDevice(customIndex, device)}
                  >
                    <Text style={[styles.actionButtonText, styles.deleteButtonText]}>
                      刪除
                    </Text>
                  </TouchableOpacity>
                )}
              </View>

              {isCustomDevice && (
                <View style={styles.customBadge}>
                  <Text style={styles.customBadgeText}>自定義</Text>
                </View>
              )}
            </View>
          );
        })
        )}
      </ScrollView>
    </SafeAreaView>
  );
};
