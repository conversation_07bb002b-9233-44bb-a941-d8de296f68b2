{"name": "epd-manager-app", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "eject": "expo eject", "test": "jest", "lint": "eslint .", "build:android": "node scripts/build-android.js preview", "build:android:dev": "node scripts/build-android.js development", "build:android:prod": "node scripts/build-android.js production", "build:icons": "node scripts/create-basic-png.js", "prebuild": "expo prebuild", "build:local": "eas build --platform android --profile development --local", "android:debug": "node scripts/debug-android-build.js"}, "dependencies": {"@react-native-async-storage/async-storage": "1.23.1", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.7", "@react-navigation/native-stack": "^6.9.13", "@tanstack/react-query": "^4.32.0", "axios": "^1.5.0", "expo": "~51.0.0", "expo-build-properties": "~0.12.3", "expo-status-bar": "~1.12.1", "react": "18.2.0", "react-native": "0.74.5", "react-native-elements": "^3.4.3", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.16.1", "react-native-permissions": "^4.1.5", "react-native-reanimated": "~3.10.1", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-udp": "^4.1.5", "react-native-vector-icons": "^10.0.0", "zustand": "^4.4.0", "expo-splash-screen": "~0.27.5", "expo-dev-client": "~4.0.25"}, "devDependencies": {"@babel/core": "^7.22.0", "@testing-library/jest-native": "^5.4.2", "@testing-library/react-native": "^12.1.3", "@types/react": "~18.2.14", "babel-jest": "^29.6.0", "eslint": "^8.45.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-native": "^4.0.0", "jest": "^29.6.0", "jest-expo": "^51.0.4", "react-test-renderer": "^18.2.0", "typescript": "^5.1.6"}, "private": true, "jest": {"preset": "jest-expo", "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)"], "setupFilesAfterEnv": ["@testing-library/jest-native/extend-expect"]}, "description": "EPD Manager App 是一個基於 React Native 開發的移動應用程序，用於管理電子紙顯示器系統。本應用程序提供了用戶認證、門店管理、網關管理、設備管理和 WebSocket 通信等功能，使用戶能夠在移動設備上方便地管理和監控電子紙顯示系統。", "directories": {"doc": "docs", "test": "test"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs"}