# EPD Manager App - WebSocket 即時更新實現

## 問題描述

原本 app 中的裝置管理和網關管理頁面雖然使用了 WebSocket，但需要下拉才會更新狀態，沒有實現真正的即時更新。

## 問題分析

1. **原有 WebSocket 服務的用途**：
   - `WebSocketService.ts` 中的 `WebSocketService` 類是用來模擬網關的
   - 它連接到服務器的網關 WebSocket 端點 (`/store/{storeId}/gateway/{gatewayId}`)
   - 主要用於發送設備狀態報告，而不是接收即時更新

2. **缺少前端 WebSocket 客戶端**：
   - app 缺少類似 web 端 `websocketClient.ts` 的前端 WebSocket 客戶端
   - 沒有訂閱服務器的設備狀態和網關狀態更新事件

## 解決方案

### 1. 新增前端 WebSocket 客戶端

在 `WebSocketService.ts` 中新增了 `FrontendWebSocketClient` 類：

```typescript
export class FrontendWebSocketClient {
  // 連接到前端 WebSocket 服務 (/ws)
  // 訂閱設備狀態更新
  // 訂閱網關狀態更新
  // 處理即時更新事件
}
```

### 2. 修改設備狀態管理

在 `deviceStore.ts` 中新增了 WebSocket 相關方法：

```typescript
interface DeviceStore {
  // WebSocket 相關
  connectWebSocket: () => Promise<boolean>;
  disconnectWebSocket: () => void;
  subscribeToRealTimeUpdates: (storeId: string) => () => void;
  isWebSocketConnected: () => boolean;
}

interface GatewayStore {
  // WebSocket 相關
  connectFrontendWebSocket: () => Promise<boolean>;
  disconnectFrontendWebSocket: () => void;
  subscribeToRealTimeUpdates: (storeId: string) => () => void;
  isFrontendWebSocketConnected: () => boolean;
}
```

### 3. 更新設備管理和網關管理頁面

在 `DeviceManagementScreen.tsx` 和 `GatewayManagementScreen.tsx` 中：

- 頁面加載時自動連接前端 WebSocket
- 訂閱當前門店的設備/網關狀態更新
- 當 WebSocket 連接成功時，停用定時刷新
- 接收到更新事件時，自動更新列表

## 實現細節

### WebSocket 連接流程

1. **連接建立**：
   ```typescript
   // 連接到前端 WebSocket 服務
   const connected = await connectWebSocket();
   ```

2. **客戶端識別**：
   ```typescript
   // 發送客戶端識別消息
   this.send({
     type: 'client_identify',
     clientType: 'frontend',
     timestamp: new Date().toISOString()
   });
   ```

3. **訂閱設備狀態**：
   ```typescript
   // 訂閱指定門店的設備狀態更新
   const unsubscribe = subscribeToRealTimeUpdates(storeId);
   ```

### 消息處理

服務器會推送以下類型的消息：

**設備相關**：
- `device_status_update`: 設備狀態更新
- `device_crud_update`: 設備 CRUD 操作更新

**網關相關**：
- `gateway_status_update`: 網關狀態更新

app 接收到這些消息後會自動更新本地設備和網關列表。

### 配置管理

新增了 `config/websocket.ts` 配置文件，支持動態獲取用戶設定的服務器 IP：

```typescript
export const WEBSOCKET_CONFIG = {
  DEFAULT: {
    HOST: API_CONFIG.DEFAULT_HOST, // 使用 API 配置中的默認主機
    PORT: '3001',
    PROTOCOL: 'ws'
  }
};

// 動態獲取用戶設定的服務器 IP
export const getUserServerIp = async (): Promise<string> => {
  const savedIp = await AsyncStorage.getItem(STORAGE_KEYS.SERVER_IP);
  return savedIp || WEBSOCKET_CONFIG.DEFAULT.HOST;
};
```

## 使用方式

### 配置服務器地址

**推薦方式**：使用應用內設置
1. 在登入頁面點擊「顯示服務器設置」
2. 輸入服務器的 IP 地址（例如：*************）
3. 系統會自動保存並用於 WebSocket 連接

**備用方式**：修改默認配置
如需修改默認設置，可在 `epd-manager-app/src/utils/constants.ts` 文件中修改：

```typescript
export const API_CONFIG = {
  DEFAULT_HOST: 'localhost',    // 默認服務器 IP
  DEFAULT_PORT: 3001,           // 固定端口
  DEFAULT_PROTOCOL: 'http',     // 協議
} as const;
```

### 自動即時更新

現在設備管理和網關管理頁面會：

1. 自動連接到 WebSocket 服務
2. 訂閱當前門店的設備/網關狀態更新
3. 接收到更新時自動刷新列表
4. 不再需要手動下拉刷新

## 注意事項

1. **服務器地址配置**：
   - 系統會自動使用用戶在登入頁面設定的服務器 IP
   - WebSocket 連接會使用相同的 IP 地址，端口固定為 3001
   - 無需手動修改代碼中的 IP 地址

2. **網絡環境**：
   - 確保 app 能夠訪問服務器的 WebSocket 端點
   - 檢查防火牆和網絡配置

3. **錯誤處理**：
   - WebSocket 連接失敗時會自動降級到定時刷新模式
   - 支持自動重連機制

4. **性能考慮**：
   - WebSocket 連接成功時會停用定時刷新
   - 使用防抖機制避免頻繁更新

## 測試驗證

### 設備管理測試
1. 啟動 app 並進入設備管理頁面
2. 檢查控制台日誌，確認 WebSocket 連接成功
3. 在 web 端或其他客戶端修改設備狀態
4. 觀察 app 是否自動更新，無需手動刷新

### 網關管理測試
1. 啟動 app 並進入網關管理頁面
2. 檢查控制台日誌，確認 WebSocket 連接成功
3. 在 web 端或其他客戶端修改網關狀態
4. 觀察 app 是否自動更新，無需手動刷新

## 故障排除

如果即時更新不工作：

1. 檢查服務器地址配置是否正確
2. 確認服務器 WebSocket 服務正常運行
3. 檢查網絡連接和防火牆設置
4. 查看控制台日誌中的錯誤信息
