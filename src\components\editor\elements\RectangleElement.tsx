import React, { useState, useEffect, useRef } from 'react';
import { TemplateElement } from '../../../types';
import { ControlHandle } from './ShapeElement';
import { constrainElementToCanvas } from '../canvasUtils';
import { Icon, IconType } from './IconComponent';

// 輔助函數：計算旋轉後矩形的四個角點
const calculateRotatedCorners = (
  x: number,
  y: number,
  width: number,
  height: number,
  rotation: number
) => {
  // 計算矩形中心點
  const centerX = x + width / 2;
  const centerY = y + height / 2;

  // 轉換角度為弧度
  const rad = (rotation * Math.PI) / 180;
  const cos = Math.cos(rad);
  const sin = Math.sin(rad);

  // 計算矩形四個角的相對坐標（相對於中心點）
  const halfWidth = width / 2;
  const halfHeight = height / 2;

  // 相對坐標
  const corners = [
    { relX: -halfWidth, relY: -halfHeight }, // 左上
    { relX: halfWidth, relY: -halfHeight },  // 右上
    { relX: halfWidth, relY: halfHeight },   // 右下
    { relX: -halfWidth, relY: halfHeight }   // 左下
  ];

  // 計算旋轉後的絕對坐標
  return corners.map(corner => {
    // 旋轉點
    const rotatedX = corner.relX * cos - corner.relY * sin;
    const rotatedY = corner.relX * sin + corner.relY * cos;

    // 轉換回絕對坐標
    return {
      x: centerX + rotatedX,
      y: centerY + rotatedY
    };
  });
};

// 檢查旋轉後的矩形是否超出畫布，並返回調整後的尺寸和位置
const constrainRotatedElement = (
  element: { x: number; y: number; width: number; height: number; rotation?: number },
  canvasWidth: number,
  canvasHeight: number
): { x: number; y: number; width: number; height: number } => {
  // 如果沒有旋轉，使用簡單的邊界檢查
  if (!element.rotation || element.rotation === 0) {
    const result = constrainElementToCanvas(element, canvasWidth, canvasHeight);
    return {
      x: result.x || element.x,
      y: result.y || element.y,
      width: result.width || element.width,
      height: result.height || element.height
    };
  }

  // 計算旋轉後的四個角點
  const corners = calculateRotatedCorners(
    element.x,
    element.y,
    element.width,
    element.height,
    element.rotation
  );

  // 計算邊界超出量
  let minOverflowX = 0;
  let maxOverflowX = 0;
  let minOverflowY = 0;
  let maxOverflowY = 0;

  // 檢查每個角點是否超出畫布
  corners.forEach(corner => {
    // X軸檢查
    if (corner.x < 0) {
      minOverflowX = Math.min(minOverflowX, corner.x);
    }
    if (corner.x > canvasWidth) {
      maxOverflowX = Math.max(maxOverflowX, corner.x - canvasWidth);
    }

    // Y軸檢查
    if (corner.y < 0) {
      minOverflowY = Math.min(minOverflowY, corner.y);
    }
    if (corner.y > canvasHeight) {
      maxOverflowY = Math.max(maxOverflowY, corner.y - canvasHeight);
    }
  });

  // 計算需要調整的x和y
  let adjustedX = element.x;
  let adjustedY = element.y;
  let adjustedWidth = element.width;
  let adjustedHeight = element.height;

  // 調整位置 - 如果超出了左邊或上邊界
  if (minOverflowX < 0) {
    adjustedX -= minOverflowX; // 向右移動元素
  }

  if (minOverflowY < 0) {
    adjustedY -= minOverflowY; // 向下移動元素
  }

  // 處理右邊和下邊超出的情況 - 通過縮小尺寸
  if (maxOverflowX > 0 || maxOverflowY > 0) {
    // 計算縮放比例，使元素能夠適應畫布
    const scaleFactorX = maxOverflowX > 0
      ? (element.width - maxOverflowX) / element.width
      : 1;
    const scaleFactorY = maxOverflowY > 0
      ? (element.height - maxOverflowY) / element.height
      : 1;

    // 使用更小的縮放因子來保證元素完全在畫布內
    const scaleFactor = Math.min(scaleFactorX, scaleFactorY, 1);

    // 如果需要縮小
    if (scaleFactor < 1) {
      // 按比例縮小
      adjustedWidth = Math.max(5, element.width * scaleFactor);
      adjustedHeight = Math.max(5, element.height * scaleFactor);

      // 調整位置，保持中心點不變
      const widthDiff = element.width - adjustedWidth;
      const heightDiff = element.height - adjustedHeight;
      adjustedX += widthDiff / 2;
      adjustedY += heightDiff / 2;
    }
  }

  // 確保元素寬高不小於最小值
  if (adjustedWidth < 5) adjustedWidth = 5;
  if (adjustedHeight < 5) adjustedHeight = 5;

  return {
    x: adjustedX,
    y: adjustedY,
    width: adjustedWidth,
    height: adjustedHeight
  };
};

interface RectangleElementProps {
  element: TemplateElement;
  isSelected: boolean;
  onSelect: (id: string, e?: React.MouseEvent) => void; // 修改: 添加事件參數
  onUpdate: (id: string, updates: Partial<TemplateElement>) => void;
  zoom?: number;
  setSelectedTool?: (tool: string | null) => void;
  // 多選功能 - 新增屬性
  selectedElementIds?: string[];
  moveSelectedElements?: (dx: number, dy: number) => void;
  isMultiMoving?: boolean; // 新增：多選移動狀態標誌
}

export const RectangleElement: React.FC<RectangleElementProps> = ({
  element,
  isSelected,
  onSelect,
  onUpdate,
  zoom = 100,
  setSelectedTool,
  // 多選功能 - 新增屬性
  selectedElementIds = [],
  moveSelectedElements,
  isMultiMoving = false // 預設為 false
}) => {
  const elementRef = useRef<HTMLDivElement>(null);

  const [isDragging, setIsDragging] = useState(false);
  const [startDragPosition, setStartDragPosition] = useState({ x: 0, y: 0 });
  const [isResizing, setIsResizing] = useState(false);
  const [activeHandle, setActiveHandle] = useState<ControlHandle | null>(null);
  const [isRotating, setIsRotating] = useState(false); // 新增: 旋轉狀態
  const [rotationStartAngle, setRotationStartAngle] = useState(0); // 新增: 旋轉初始角度

  // 矩形線條寬度和顏色
  const lineWidth = element.lineWidth || 1;
  const lineColor = element.lineColor || '#000000';
  const fillColor = element.fillColor || 'transparent';

  // 是否為多選狀態
  const isMultiSelected = selectedElementIds.length > 1 && selectedElementIds.includes(element.id);

  // 處理滑鼠按下事件 - 開始拖曳
  const handleMouseDown = (e: React.MouseEvent) => {
    if (!elementRef.current || isResizing || isRotating) return;

    e.stopPropagation();
    setIsDragging(true);
    setStartDragPosition({ x: e.clientX, y: e.clientY });

    // 確保元素被選中
    if (!isSelected) {
      onSelect(element.id);
    }
  };

  // 處理控制點滑鼠按下事件 - 開始調整大小或旋轉
  const handleControlPointMouseDown = (handle: ControlHandle, e: React.MouseEvent) => {
    e.stopPropagation();

    if (handle === ControlHandle.Rotate) {
      // 旋轉處理
      setIsRotating(true);
      // 計算元素中心點
      const rect = elementRef.current?.getBoundingClientRect();
      if (rect) {
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        // 計算滑鼠初始位置與中心點的角度
        const startAngle = Math.atan2(
          e.clientY - centerY,
          e.clientX - centerX
        ) * (180 / Math.PI);
        setRotationStartAngle(startAngle - (element.rotation || 0));
      }
    } else {
      // 調整大小處理
      setIsResizing(true);
      setActiveHandle(handle);
    }
  };

  // 處理滑鼠移動事件 - 拖曳、調整大小、旋轉
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging) {
        // 元素拖曳移動
        const deltaX = e.clientX - startDragPosition.x;
        const deltaY = e.clientY - startDragPosition.y;

        // 根據縮放比例調整移動量
        const scaledDeltaX = deltaX / (zoom / 100);
        const scaledDeltaY = deltaY / (zoom / 100);

        // 多選狀態下移動所有選中元素
        if (isMultiSelected && moveSelectedElements) {
          moveSelectedElements(scaledDeltaX, scaledDeltaY);
        } else {
          // 單選狀態下只移動當前元素
          // 計算新位置
          const newX = Math.round(element.x + scaledDeltaX);
          const newY = Math.round(element.y + scaledDeltaY);

          // 獲取畫布的寬高
          const canvasElement = elementRef.current?.closest('[data-canvas-width]');
          const canvasWidth = canvasElement ?
            parseInt(canvasElement.getAttribute('data-canvas-width') || '250', 10) : 250;
          const canvasHeight = canvasElement ?
            parseInt(canvasElement.getAttribute('data-canvas-height') || '122', 10) : 122;

          // 使用限制函數確保元素在畫布內
          const constrainedUpdates = constrainElementToCanvas(
            { x: newX, y: newY, width: Math.round(element.width), height: Math.round(element.height) },
            canvasWidth,
            canvasHeight
          );

          // 更新元素位置
          onUpdate(element.id, constrainedUpdates);
        }

        setStartDragPosition({ x: e.clientX, y: e.clientY });
      } else if (isRotating && elementRef.current) {
        // 處理旋轉
        const rect = elementRef.current.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        // 計算當前角度
        const currentAngle = Math.atan2(
          e.clientY - centerY,
          e.clientX - centerX
        ) * (180 / Math.PI);

        // 計算旋轉差值（考慮初始偏移）
        let newRotation = currentAngle - rotationStartAngle;

        // 按住Shift鍵時，將角度限制為15度的倍數
        if (e.shiftKey) {
          newRotation = Math.round(newRotation / 15) * 15;
        }

        // 更新元素旋轉角度
        onUpdate(element.id, { rotation: newRotation });
      } else if (isResizing && activeHandle) {
        // 獲取元素的旋轉角度
        const rotation = element.rotation || 0;
        // 將角度轉換為弧度
        const rotationRad = (rotation * Math.PI) / 180;
        const cos = Math.cos(rotationRad);
        const sin = Math.sin(rotationRad);

        // 獲取畫布的寬高
        const canvasElement = elementRef.current?.closest('[data-canvas-width]');
        const canvasWidth = canvasElement ?
          parseInt(canvasElement.getAttribute('data-canvas-width') || '250', 10) : 250;
        const canvasHeight = canvasElement ?
          parseInt(canvasElement.getAttribute('data-canvas-height') || '122', 10) : 122;

        // 根據縮放比例調整移動量
        let scaledMovementX = e.movementX / (zoom / 100);
        let scaledMovementY = e.movementY / (zoom / 100);

        // 如果元素已旋轉，則轉換滑鼠移動量到旋轉後的坐標系
        if (rotation !== 0) {
          // 計算在旋轉坐標系中的移動量
          const rotatedMovementX = scaledMovementX * cos + scaledMovementY * sin;
          const rotatedMovementY = -scaledMovementX * sin + scaledMovementY * cos;

          scaledMovementX = rotatedMovementX;
          scaledMovementY = rotatedMovementY;
        }

        // 根據控制點位置計算新的尺寸和位置
        let newX = Math.round(element.x);
        let newY = Math.round(element.y);
        let newWidth = Math.round(element.width);
        let newHeight = Math.round(element.height);

        // 根據不同的控制點處理不同的調整邏輯
        switch (activeHandle) {
          case ControlHandle.TopLeft:
            newX = Math.round(element.x + scaledMovementX);
            newY = Math.round(element.y + scaledMovementY);
            newWidth = Math.round(element.width - scaledMovementX);
            newHeight = Math.round(element.height - scaledMovementY);

            // 如果是正方形，則保持長寬相等
            if (element.type === 'square') {
              // 使用較小的值來確保兩邊相等
              const minChange = Math.min(newWidth, newHeight);
              newWidth = minChange;
              newHeight = minChange;
              newX = element.x + (element.width - minChange);
              newY = element.y + (element.height - minChange);
            }
            break;
          case ControlHandle.TopRight:
            newY = Math.round(element.y + scaledMovementY);
            newWidth = Math.round(element.width + scaledMovementX);
            newHeight = Math.round(element.height - scaledMovementY);

            // 如果是正方形，則保持長寬相等
            if (element.type === 'square') {
              // 使用較小的值來確保兩邊相等
              const minChange = Math.min(newWidth, newHeight);
              newWidth = minChange;
              newHeight = minChange;
              newY = element.y + (element.height - minChange);
            }
            break;
          case ControlHandle.BottomLeft:
            newX = Math.round(element.x + scaledMovementX);
            newWidth = Math.round(element.width - scaledMovementX);
            newHeight = Math.round(element.height + scaledMovementY);

            // 如果是正方形，則保持長寬相等
            if (element.type === 'square') {
              // 使用較小的值來確保兩邊相等
              const minChange = Math.min(newWidth, newHeight);
              newWidth = minChange;
              newHeight = minChange;
              newX = element.x + (element.width - minChange);
            }
            break;
          case ControlHandle.BottomRight:
            newWidth = Math.round(element.width + scaledMovementX);
            newHeight = Math.round(element.height + scaledMovementY);

            // 如果是正方形，則保持長寬相等
            if (element.type === 'square') {
              // 使用較大的值讓用戶能夠容易調整大小
              const maxChange = Math.max(newWidth, newHeight);
              newWidth = maxChange;
              newHeight = maxChange;
            }
            break;
          case ControlHandle.Top:
            newY = Math.round(element.y + scaledMovementY);
            newHeight = Math.round(element.height - scaledMovementY);

            // 如果是正方形，則保持長寬相等
            if (element.type === 'square') {
              newWidth = newHeight;
              // 中心點不變的調整
              const widthDiff = element.width - newWidth;
              newX = element.x + widthDiff / 2;
            }
            break;
          case ControlHandle.Right:
            newWidth = Math.round(element.width + scaledMovementX);

            // 如果是正方形，則保持長寬相等
            if (element.type === 'square') {
              newHeight = newWidth;
              // 中心點不變的調整
              const heightDiff = element.height - newHeight;
              newY = element.y + heightDiff / 2;
            }
            break;
          case ControlHandle.Bottom:
            newHeight = Math.round(element.height + scaledMovementY);

            // 如果是正方形，則保持長寬相等
            if (element.type === 'square') {
              newWidth = newHeight;
              // 中心點不變的調整
              const widthDiff = element.width - newWidth;
              newX = element.x + widthDiff / 2;
            }
            break;
          case ControlHandle.Left:
            newX = Math.round(element.x + scaledMovementX);
            newWidth = Math.round(element.width - scaledMovementX);

            // 如果是正方形，則保持長寬相等
            if (element.type === 'square') {
              newHeight = newWidth;
              // 中心點不變的調整
              const heightDiff = element.height - newHeight;
              newY = element.y + heightDiff / 2;
            }
            break;
        }

        // 確保最小尺寸
        if (newWidth < 5) newWidth = 5;
        if (newHeight < 5) newHeight = 5;

        // 處理旋轉後的邊界檢查
        const newElement = {
          x: newX,
          y: newY,
          width: newWidth,
          height: newHeight,
          rotation: element.rotation
        };

        // 使用旋轉後邊界檢查並獲取調整後的位置和尺寸
        const constrainedElement = constrainRotatedElement(
          newElement,
          canvasWidth,
          canvasHeight
        );

        // 更新元素
        onUpdate(element.id, constrainedElement);
      }
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      setIsResizing(false);
      setIsRotating(false);
      setActiveHandle(null);
    };

    if (isDragging || isResizing || isRotating) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [
    isDragging,
    isResizing,
    isRotating,
    startDragPosition,
    activeHandle,
    rotationStartAngle,
    element,
    onUpdate,
    zoom,
    isMultiSelected,
    moveSelectedElements
  ]);

  return (
    <div
      ref={elementRef}
      style={{
        position: 'absolute',
        left: element.x,
        top: element.y,
        width: element.width,
        height: element.height,
        cursor: isSelected ? 'move' : 'pointer',
        border: element.type === 'icon' ? 'none' : `${lineWidth}px solid ${lineColor}`,
        // 多選效果：當元素被多選時，添加特殊背景顏色
        backgroundColor: isMultiSelected ? 'rgba(59, 130, 246, 0.15)' : (element.type === 'icon' ? 'transparent' : fillColor),
        transform: element.rotation ? `rotate(${element.rotation}deg)` : undefined,
        transformOrigin: 'center center',
        // 改進選中效果
        outline: isSelected ? '1px dashed #3b82f6' : 'none',
        outlineOffset: '2px',
        // 提升選中元素的層級
        zIndex: isSelected ? 10 : 1,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}
      onClick={(e) => {
        e.stopPropagation();

        // 如果正在進行多選移動，則不處理點擊選擇事件
        if (isMultiMoving) {
          console.log('處於多選移動狀態，忽略矩形選擇操作');
          return;
        }

        // 處理Shift多選 - 修改: 使用事件參數而不是window.event
        if (e.shiftKey && selectedElementIds.length > 0) {
          // 多選模式下的點擊處理由父組件處理
          onSelect(element.id, e);
        } else {
          // 普通點擊，選中單個元素
          onSelect(element.id, e);
          // 選中元素時取消工具選擇
          if (setSelectedTool) {
            setSelectedTool(null);
          }
        }
      }}
      onMouseDown={handleMouseDown}
      data-element-id={element.id}
    >
      {/* 顯示圖標元素 */}
      {element.type === 'icon' && (
        <Icon
          iconType={(element.iconType as IconType) || 'star'}
          size={Math.min(element.width, element.height) * 0.8}
          color={element.lineColor || '#000000'}
          strokeWidth={element.lineWidth || 2}
        />
      )}

      {/* 只在單選狀態下顯示控制點，多選狀態下不顯示 */}
      {isSelected && !isMultiSelected && (
        <>
          {/* 角落控制點 */}
          <div
            style={{
              position: 'absolute',
              left: -4,
              top: -4,
              width: 8,
              height: 8,
              backgroundColor: '#3b82f6',
              border: '1px solid white',
              borderRadius: '50%',
              cursor: 'nwse-resize',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.TopLeft, e)}
          />
          <div
            style={{
              position: 'absolute',
              right: -4,
              top: -4,
              width: 8,
              height: 8,
              backgroundColor: '#3b82f6',
              border: '1px solid white',
              borderRadius: '50%',
              cursor: 'nesw-resize',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.TopRight, e)}
          />
          <div
            style={{
              position: 'absolute',
              left: -4,
              bottom: -4,
              width: 8,
              height: 8,
              backgroundColor: '#3b82f6',
              border: '1px solid white',
              borderRadius: '50%',
              cursor: 'nesw-resize',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.BottomLeft, e)}
          />
          <div
            style={{
              position: 'absolute',
              right: -4,
              bottom: -4,
              width: 8,
              height: 8,
              backgroundColor: '#3b82f6',
              border: '1px solid white',
              borderRadius: '50%',
              cursor: 'nwse-resize',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.BottomRight, e)}
          />

          {/* 邊緣控制點 */}
          <div
            style={{
              position: 'absolute',
              left: '50%',
              top: -4,
              width: 8,
              height: 8,
              marginLeft: -4,
              backgroundColor: '#3b82f6',
              border: '1px solid white',
              borderRadius: '50%',
              cursor: 'ns-resize',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.Top, e)}
          />
          <div
            style={{
              position: 'absolute',
              right: -4,
              top: '50%',
              width: 8,
              height: 8,
              marginTop: -4,
              backgroundColor: '#3b82f6',
              border: '1px solid white',
              borderRadius: '50%',
              cursor: 'ew-resize',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.Right, e)}
          />
          <div
            style={{
              position: 'absolute',
              left: '50%',
              bottom: -4,
              width: 8,
              height: 8,
              marginLeft: -4,
              backgroundColor: '#3b82f6',
              border: '1px solid white',
              borderRadius: '50%',
              cursor: 'ns-resize',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.Bottom, e)}
          />
          <div
            style={{
              position: 'absolute',
              left: -4,
              top: '50%',
              width: 8,
              height: 8,
              marginTop: -4,
              backgroundColor: '#3b82f6',
              border: '1px solid white',
              borderRadius: '50%',
              cursor: 'ew-resize',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.Left, e)}
          />

          {/* 旋轉控制點 */}
          <div
            style={{
              position: 'absolute',
              left: '50%',
              top: -25,
              width: 8, // 稍微調大一點，使虛線效果更明顯
              height: 8,
              marginLeft: -4,
              backgroundColor: 'transparent', // 改為透明背景
              border: '0.5px dashed #3b82f6', // 改為虛線邊框
              borderRadius: '50%',
              cursor: 'url("data:image/svg+xml,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22%3E%3Cpath fill=%22%23000000%22 d=%22M7.11 8.53L5.7 7.11C4.8 8.27 4.24 9.61 4.07 11h2.02c.14-.87.49-1.72 1.02-2.47zM6.09 13H4.07c.17 1.39.72 2.73 1.62 3.89l1.41-1.42c-.52-.75-.87-1.59-1.01-2.47zm1.01 5.32c1.16.9 2.51 1.44 3.9 1.61V17.9c-.87-.15-1.71-.49-2.46-1.03L7.1 18.32zM13 4.07V1L8.45 5.55 13 10V6.09c2.84.48 5 2.94 5 5.91s-2.16 5.43-5 5.91v2.02c3.95-.49 7-3.85 7-7.93s-3.05-7.44-7-7.93z%22/%3E%3C/svg%3E") 12 12, auto',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.Rotate, e)}
          />

          {/* 旋轉控制點連接線 */}
          <div
            style={{
              position: 'absolute',
              left: '50%',
              top: -15,
              width: 1,
              height: 15,
              marginLeft: -0.5,
              backgroundColor: '#3b82f6',
              zIndex: 99
            }}
          />
        </>
      )}
    </div>
  );
};