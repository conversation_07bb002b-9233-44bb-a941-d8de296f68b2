import React, { useState, useEffect, useRef } from 'react';
import { TemplateElement } from '../../../types';
import { ControlHandle } from './ShapeElement';
import { constrainElementToCanvas } from '../canvasUtils';

interface CircleElementProps {
  element: TemplateElement;
  isSelected: boolean;
  onSelect: (id: string, e?: React.MouseEvent) => void;
  onUpdate: (id: string, updates: Partial<TemplateElement>) => void;
  zoom?: number;
  setSelectedTool?: (tool: string | null) => void;
  // 多選功能 - 新增屬性
  selectedElementIds?: string[];
  moveSelectedElements?: (dx: number, dy: number) => void;
  isMultiMoving?: boolean;
}

export const CircleElement: React.FC<CircleElementProps> = ({
  element,
  isSelected,
  onSelect,
  onUpdate,
  zoom = 100,
  setSelectedTool,
  // 多選功能 - 新增屬性
  selectedElementIds = [],
  moveSelectedElements,
  isMultiMoving = false
}) => {
  const elementRef = useRef<HTMLDivElement>(null);

  const [isDragging, setIsDragging] = useState(false);
  const [startDragPosition, setStartDragPosition] = useState({ x: 0, y: 0 });
  const [isResizing, setIsResizing] = useState(false);
  const [activeHandle, setActiveHandle] = useState<ControlHandle | null>(null);
  const [isRotating, setIsRotating] = useState(false);
  const [rotationStartAngle, setRotationStartAngle] = useState(0);

  // 線條寬度和顏色
  const lineWidth = element.lineWidth || 1;
  const lineColor = element.lineColor || '#000000';
  const fillColor = element.fillColor || 'transparent';

  // 是否為多選狀態
  const isMultiSelected = selectedElementIds.length > 1 && selectedElementIds.includes(element.id);

  // 處理滑鼠按下事件 - 開始拖曳
  const handleMouseDown = (e: React.MouseEvent) => {
    if (!elementRef.current || isResizing || isRotating) return;

    e.stopPropagation();
    setIsDragging(true);
    setStartDragPosition({ x: e.clientX, y: e.clientY });

    // 確保元素被選中
    if (!isSelected) {
      onSelect(element.id);
    }
  };

  // 處理控制點滑鼠按下事件 - 開始調整大小或旋轉
  const handleControlPointMouseDown = (handle: ControlHandle, e: React.MouseEvent) => {
    e.stopPropagation();

    if (handle === ControlHandle.Rotate) {
      // 旋轉處理
      setIsRotating(true);
      // 計算元素中心點
      const rect = elementRef.current?.getBoundingClientRect();
      if (rect) {
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        // 計算滑鼠初始位置與中心點的角度
        const startAngle = Math.atan2(
          e.clientY - centerY,
          e.clientX - centerX
        ) * (180 / Math.PI);
        setRotationStartAngle(startAngle - (element.rotation || 0));
      }
    } else {
      // 調整大小處理
      setIsResizing(true);
      setActiveHandle(handle);
    }
  };

  // 處理滑鼠移動事件 - 拖曳、調整大小、旋轉
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging) {
        // 元素拖曳移動
        const deltaX = e.clientX - startDragPosition.x;
        const deltaY = e.clientY - startDragPosition.y;

        // 根據縮放比例調整移動量
        const scaledDeltaX = deltaX / (zoom / 100);
        const scaledDeltaY = deltaY / (zoom / 100);

        // 多選狀態下移動所有選中元素
        if (isMultiSelected && moveSelectedElements) {
          moveSelectedElements(scaledDeltaX, scaledDeltaY);
        } else {
          // 單選狀態下只移動當前元素
          // 計算新位置
          const newX = Math.round(element.x + scaledDeltaX);
          const newY = Math.round(element.y + scaledDeltaY);

          // 獲取畫布的寬高
          const canvasElement = elementRef.current?.closest('[data-canvas-width]');
          const canvasWidth = canvasElement ?
            parseInt(canvasElement.getAttribute('data-canvas-width') || '250', 10) : 250;
          const canvasHeight = canvasElement ?
            parseInt(canvasElement.getAttribute('data-canvas-height') || '122', 10) : 122;

          // 使用限制函數確保元素在畫布內
          const constrainedUpdates = constrainElementToCanvas(
            { x: newX, y: newY, width: Math.round(element.width), height: Math.round(element.height) },
            canvasWidth,
            canvasHeight
          );

          // 更新元素位置
          onUpdate(element.id, constrainedUpdates);
        }

        setStartDragPosition({ x: e.clientX, y: e.clientY });
      } else if (isRotating && elementRef.current) {
        // 處理旋轉
        const rect = elementRef.current.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        // 計算當前角度
        const currentAngle = Math.atan2(
          e.clientY - centerY,
          e.clientX - centerX
        ) * (180 / Math.PI);

        // 計算旋轉差值（考慮初始偏移）
        let newRotation = currentAngle - rotationStartAngle;

        // 按住Shift鍵時，將角度限制為15度的倍數
        if (e.shiftKey) {
          newRotation = Math.round(newRotation / 15) * 15;
        }

        // 更新元素旋轉角度
        onUpdate(element.id, { rotation: newRotation });
      } else if (isResizing && activeHandle) {
        // 獲取元素的旋轉角度
        const rotation = element.rotation || 0;
        // 將角度轉換為弧度
        const rotationRad = (rotation * Math.PI) / 180;
        const cos = Math.cos(rotationRad);
        const sin = Math.sin(rotationRad);

        // 獲取畫布的寬高
        const canvasElement = elementRef.current?.closest('[data-canvas-width]');
        const canvasWidth = canvasElement ?
          parseInt(canvasElement.getAttribute('data-canvas-width') || '250', 10) : 250;
        const canvasHeight = canvasElement ?
          parseInt(canvasElement.getAttribute('data-canvas-height') || '122', 10) : 122;

        // 根據縮放比例調整移動量
        let scaledMovementX = e.movementX / (zoom / 100);
        let scaledMovementY = e.movementY / (zoom / 100);

        // 如果元素已旋轉，則轉換滑鼠移動量到旋轉後的坐標系
        if (rotation !== 0) {
          // 計算在旋轉坐標系中的移動量
          const rotatedMovementX = scaledMovementX * cos + scaledMovementY * sin;
          const rotatedMovementY = -scaledMovementX * sin + scaledMovementY * cos;

          scaledMovementX = rotatedMovementX;
          scaledMovementY = rotatedMovementY;
        }

        // 根據控制點位置計算新的尺寸和位置
        let newX = Math.round(element.x);
        let newY = Math.round(element.y);
        let newWidth = Math.round(element.width);
        let newHeight = Math.round(element.height);

        // 圓形需要保持寬高相等
        const isCircle = element.type === 'circle';

        // 根據不同的控制點處理不同的調整邏輯
        switch (activeHandle) {
          case ControlHandle.Top:
            newY = Math.round(element.y + scaledMovementY);
            newHeight = Math.round(element.height - scaledMovementY);

            // 如果是圓形，則保持長寬相等
            if (isCircle) {
              newWidth = newHeight;
              // 中心點不變的調整
              const widthDiff = element.width - newWidth;
              newX = Math.round(element.x + widthDiff / 2);
            }
            break;
          case ControlHandle.Right:
            newWidth = Math.round(element.width + scaledMovementX);

            // 如果是圓形，則保持長寬相等
            if (isCircle) {
              newHeight = newWidth;
              // 中心點不變的調整
              const heightDiff = element.height - newHeight;
              newY = Math.round(element.y + heightDiff / 2);
            }
            break;
          case ControlHandle.Bottom:
            newHeight = Math.round(element.height + scaledMovementY);

            // 如果是圓形，則保持長寬相等
            if (isCircle) {
              newWidth = newHeight;
              // 中心點不變的調整
              const widthDiff = element.width - newWidth;
              newX = Math.round(element.x + widthDiff / 2);
            }
            break;
          case ControlHandle.Left:
            newX = Math.round(element.x + scaledMovementX);
            newWidth = Math.round(element.width - scaledMovementX);

            // 如果是圓形，則保持長寬相等
            if (isCircle) {
              newHeight = newWidth;
              // 中心點不變的調整
              const heightDiff = element.height - newHeight;
              newY = Math.round(element.y + heightDiff / 2);
            }
            break;
        }

        // 確保最小尺寸
        if (newWidth < 5) newWidth = 5;
        if (newHeight < 5) newHeight = 5;

        // 確保圓形的寬高相等
        if (isCircle) {
          const size = Math.max(newWidth, newHeight);
          newWidth = size;
          newHeight = size;
        }

        // 使用限制函數確保元素在畫布內
        const constrainedElement = constrainElementToCanvas(
          {
            x: newX,
            y: newY,
            width: newWidth,
            height: newHeight,
            rotation: element.rotation
          },
          canvasWidth,
          canvasHeight
        );

        // 更新元素
        onUpdate(element.id, constrainedElement);
      }
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      setIsResizing(false);
      setIsRotating(false);
      setActiveHandle(null);
    };

    if (isDragging || isResizing || isRotating) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [
    isDragging,
    isResizing,
    isRotating,
    startDragPosition,
    activeHandle,
    rotationStartAngle,
    element,
    onUpdate,
    zoom,
    isMultiSelected,
    moveSelectedElements
  ]);

  return (
    <div
      ref={elementRef}
      style={{
        position: 'absolute',
        left: element.x,
        top: element.y,
        width: element.width,
        height: element.height,
        cursor: isSelected ? 'move' : 'pointer',
        border: `${lineWidth}px solid ${lineColor}`,
        borderRadius: '50%', // 使用圓角讓它看起來像圓形/橢圓形
        backgroundColor: isMultiSelected ? 'rgba(59, 130, 246, 0.15)' : fillColor,
        transform: element.rotation ? `rotate(${element.rotation}deg)` : undefined,
        transformOrigin: 'center center',
        outline: isSelected ? '1px dashed #3b82f6' : 'none',
        outlineOffset: '2px',
        zIndex: isSelected ? 10 : 1
      }}
      onClick={(e) => {
        e.stopPropagation();

        // 如果正在進行多選移動，則不處理點擊選擇事件
        if (isMultiMoving) {
          console.log('處於多選移動狀態，忽略圓形選擇操作');
          return;
        }

        // 處理Shift多選
        if (e.shiftKey && selectedElementIds.length > 0) {
          // 多選模式下的點擊處理由父組件處理
          onSelect(element.id, e);
        } else {
          // 普通點擊，選中單個元素
          onSelect(element.id, e);
          // 選中元素時取消工具選擇
          if (setSelectedTool) {
            setSelectedTool(null);
          }
        }
      }}
      onMouseDown={handleMouseDown}
      data-element-id={element.id}
    >
      {/* 只在單選狀態下顯示控制點，多選狀態下不顯示 */}
      {isSelected && !isMultiSelected && (
        <>
          {/* 邊緣控制點 - 圓形/橢圓只使用上下左右四個控制點 */}
          <div
            style={{
              position: 'absolute',
              left: '50%',
              top: -4,
              width: 8,
              height: 8,
              marginLeft: -4,
              backgroundColor: '#3b82f6',
              border: '1px solid white',
              borderRadius: '50%',
              cursor: 'ns-resize',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.Top, e)}
          />
          <div
            style={{
              position: 'absolute',
              right: -4,
              top: '50%',
              width: 8,
              height: 8,
              marginTop: -4,
              backgroundColor: '#3b82f6',
              border: '1px solid white',
              borderRadius: '50%',
              cursor: 'ew-resize',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.Right, e)}
          />
          <div
            style={{
              position: 'absolute',
              left: '50%',
              bottom: -4,
              width: 8,
              height: 8,
              marginLeft: -4,
              backgroundColor: '#3b82f6',
              border: '1px solid white',
              borderRadius: '50%',
              cursor: 'ns-resize',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.Bottom, e)}
          />
          <div
            style={{
              position: 'absolute',
              left: -4,
              top: '50%',
              width: 8,
              height: 8,
              marginTop: -4,
              backgroundColor: '#3b82f6',
              border: '1px solid white',
              borderRadius: '50%',
              cursor: 'ew-resize',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.Left, e)}
          />

          {/* 旋轉控制點 */}
          <div
            style={{
              position: 'absolute',
              left: '50%',
              top: -25,
              width: 8,
              height: 8,
              marginLeft: -4,
              backgroundColor: 'transparent',
              border: '0.5px dashed #3b82f6',
              borderRadius: '50%',
              cursor: 'url("data:image/svg+xml,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 width=%2224%22 height=%2224%22 viewBox=%220 0 24 24%22%3E%3Cpath fill=%22%23000000%22 d=%22M7.11 8.53L5.7 7.11C4.8 8.27 4.24 9.61 4.07 11h2.02c.14-.87.49-1.72 1.02-2.47zM6.09 13H4.07c.17 1.39.72 2.73 1.62 3.89l1.41-1.42c-.52-.75-.87-1.59-1.01-2.47zm1.01 5.32c1.16.9 2.51 1.44 3.9 1.61V17.9c-.87-.15-1.71-.49-2.46-1.03L7.1 18.32zM13 4.07V1L8.45 5.55 13 10V6.09c2.84.48 5 2.94 5 5.91s-2.16 5.43-5 5.91v2.02c3.95-.49 7-3.85 7-7.93s-3.05-7.44-7-7.93z%22/%3E%3C/svg%3E") 12 12, auto',
              zIndex: 100
            }}
            onMouseDown={(e) => handleControlPointMouseDown(ControlHandle.Rotate, e)}
          />

          {/* 旋轉控制點連接線 */}
          <div
            style={{
              position: 'absolute',
              left: '50%',
              top: -15,
              width: 1,
              height: 15,
              marginLeft: -0.5,
              backgroundColor: '#3b82f6',
              zIndex: 99
            }}
          />
        </>
      )}
    </div>
  );
};