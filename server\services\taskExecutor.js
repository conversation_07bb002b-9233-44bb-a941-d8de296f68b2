const { ObjectId } = require('mongodb');
const { getScreenSizeMap, getScreenConfigBySize, DisplayColorType } = require('../utils/screenConfigs');

// 使用共享的 MongoDB 連接
let getDbConnection = null;

// 初始化資料庫連接函數
const initDB = (dbConnectionFunc) => {
  getDbConnection = dbConnectionFunc;
};

class TaskExecutor {
  constructor() {
    // 初始化時不需要導入，在需要時動態獲取
  }

  async executeTask(task, user) {
    try {
      switch (task.intent) {
        case 'create_store':
          return await this.createStore(task.extractedData, user);
        case 'create_template':
          return await this.createTemplate(task.extractedData, user);
        case 'create_account':
          return await this.createAccount(task.extractedData, user);
        default:
          throw new Error(`不支援的任務類型: ${task.intent}`);
      }
    } catch (error) {
      console.error('任務執行失敗:', error);
      return {
        success: false,
        error: error.message,
        message: '任務執行失敗'
      };
    }
  }

  async createStore(storeData, user) {
    try {
      // 標準化字段名稱 - 處理AI提取的數據字段名稱
      const normalizedData = {
        name: storeData.storeName || storeData.name,
        address: storeData.storeAddress || storeData.address,
        phone: storeData.storePhone || storeData.phone,
        id: storeData.storeId || storeData.id,
        managerId: storeData.managerId,
        importSystemData: storeData.importSystemData
      };

      // 驗證必要字段
      if (!normalizedData.name) {
        throw new Error('門店名稱為必填項');
      }

      // 生成門店ID（如果未提供）
      if (!normalizedData.id) {
        normalizedData.id = this.generateStoreId(normalizedData.name);
      }

      // 獲取數據庫連接
      if (!getDbConnection) {
        throw new Error('資料庫連接函數尚未初始化');
      }
      const { db } = await getDbConnection();
      const storesCollection = db.collection('stores');

      // 檢查門店ID是否重複
      const existingStore = await storesCollection.findOne({ id: normalizedData.id });
      if (existingStore) {
        // 如果ID重複，生成新的ID
        normalizedData.id = this.generateStoreId(normalizedData.name);
      }

      // 設定默認值
      const now = new Date();
      const storePayload = {
        id: normalizedData.id,
        name: normalizedData.name,
        address: normalizedData.address || '',
        phone: normalizedData.phone || '',
        managerId: normalizedData.managerId ? new ObjectId(normalizedData.managerId) : user._id,
        status: 'active',
        createdAt: now,
        updatedAt: now,
        storeSpecificData: [],
        gatewayManagement: {},
        deviceManagement: {},
        storeSettings: {}
      };

      // 如果需要導入系統數據
      if (normalizedData.importSystemData !== false) {
        try {
          const systemDataCollection = db.collection('systemSpecificData');
          const systemData = await systemDataCollection.find({}).toArray();

          // 複製系統數據到門店
          storePayload.storeSpecificData = systemData.map(item => ({
            ...item,
            _id: new ObjectId(),
            storeId: normalizedData.id,
            createdAt: now,
            updatedAt: now
          }));
        } catch (error) {
          console.warn('導入系統數據失敗，但門店創建繼續:', error);
        }
      }

      // 創建門店
      const result = await storesCollection.insertOne(storePayload);
      const newStore = await storesCollection.findOne({ _id: result.insertedId });

      return {
        success: true,
        data: newStore,
        message: `門店 "${normalizedData.name}" 創建成功`
      };
    } catch (error) {
      console.error('創建門店失敗:', error);
      return {
        success: false,
        error: error.message,
        message: '門店創建失敗'
      };
    }
  }

  async createTemplate(templateData, user) {
    try {
      // 標準化字段名稱 - 處理AI提取的數據字段名稱
      const normalizedData = {
        name: templateData.templateName || templateData.name,
        screenSize: templateData.screen_size || templateData.screenSize,
        color: templateData.color_type || templateData.color,
        orientation: templateData.orientation,
        elements: templateData.elements,
        templateType: templateData.template_type || templateData.templateType,
        storeName: templateData.store_name || templateData.storeName,
        storeId: templateData.store_id || templateData.storeId,
        isSystemTemplate: templateData.isSystemTemplate
      };

      // 解析螢幕尺寸（如果是描述性文字）
      if (normalizedData.screenSize && typeof normalizedData.screenSize === 'string') {
        normalizedData.screenSize = this.parseScreenSize(normalizedData.screenSize);
      }

      // 解析顏色類型（如果是描述性文字）
      if (normalizedData.color && typeof normalizedData.color === 'string') {
        normalizedData.color = this.parseColorType(normalizedData.color);
      }

      // 智能判定顏色類型
      const colorResult = await this.smartColorDetection(normalizedData);
      if (colorResult.needsUserInput) {
        // 需要用戶選擇顏色
        throw new Error(`請指定顏色類型。該螢幕尺寸支持的顏色：${colorResult.availableColors.join('、')}`);
      }
      normalizedData.color = colorResult.selectedColor;

      // 解析方向（如果是描述性文字）
      if (normalizedData.orientation && typeof normalizedData.orientation === 'string') {
        normalizedData.orientation = this.parseOrientation(normalizedData.orientation);
      }

      // 處理模板類型邏輯
      await this.processTemplateType(normalizedData);

      // 驗證必要字段
      if (!normalizedData.name || !normalizedData.screenSize) {
        throw new Error('模板名稱和螢幕尺寸為必填項');
      }

      // 獲取數據庫連接
      if (!getDbConnection) {
        throw new Error('資料庫連接函數尚未初始化');
      }
      const { db } = await getDbConnection();
      const templatesCollection = db.collection('templates');

      // 設定默認值
      const now = new Date();
      const templatePayload = {
        id: Date.now().toString(),
        name: normalizedData.name,
        type: 'SINGLE_DATA',
        screenSize: normalizedData.screenSize,
        color: normalizedData.color || 'BW',
        orientation: normalizedData.orientation || 'landscape',
        elements: normalizedData.elements || [],
        isSystemTemplate: normalizedData.isSystemTemplate || false,
        storeId: normalizedData.storeId || null,
        createdAt: now,
        updatedAt: now,
        createdBy: user._id
      };

      // 檢查模板名稱是否重複（在同一門店內）
      const query = {
        name: normalizedData.name,
        ...(normalizedData.storeId ? { storeId: normalizedData.storeId } : { isSystemTemplate: true })
      };

      const existingTemplate = await templatesCollection.findOne(query);
      if (existingTemplate) {
        // 如果名稱重複，添加時間戳
        templatePayload.name = `${normalizedData.name}_${Date.now()}`;
      }

      // 創建模板
      const result = await templatesCollection.insertOne(templatePayload);
      const newTemplate = await templatesCollection.findOne({ _id: result.insertedId });

      // 生成成功消息
      let successMessage = `模板 "${templatePayload.name}" 創建成功`;
      if (normalizedData.isSystemTemplate) {
        successMessage += '（系統模板）';
      } else if (normalizedData.storeId) {
        // 獲取門店名稱用於顯示
        try {
          const { db } = await getDbConnection();
          const storesCollection = db.collection('stores');
          const store = await storesCollection.findOne({ id: normalizedData.storeId });
          if (store) {
            successMessage += `（${store.name} 門店模板）`;
          }
        } catch (error) {
          console.warn('獲取門店名稱失敗:', error);
        }
      }

      return {
        success: true,
        data: newTemplate,
        message: successMessage
      };
    } catch (error) {
      console.error('創建模板失敗:', error);
      return {
        success: false,
        error: error.message,
        message: '模板創建失敗'
      };
    }
  }

  async createAccount(userData, user) {
    try {
      // 驗證必要字段
      if (!userData.username) {
        throw new Error('用戶名為必填項');
      }

      // 獲取數據庫連接
      if (!getDbConnection) {
        throw new Error('資料庫連接函數尚未初始化');
      }
      const { db } = await getDbConnection();
      const usersCollection = db.collection('users');

      // 檢查用戶名是否重複
      const existingUser = await usersCollection.findOne({ username: userData.username });
      if (existingUser) {
        throw new Error(`用戶名 "${userData.username}" 已存在`);
      }

      // 生成默認密碼（如果未提供）
      const password = userData.password || this.generatePassword();

      // 加密密碼
      const bcrypt = require('bcryptjs');
      const hashedPassword = await bcrypt.hash(password, 10);

      // 設定默認值
      const now = new Date();
      const userPayload = {
        username: userData.username,
        password: hashedPassword,
        name: userData.name || userData.username,
        email: userData.email || '',
        phone: userData.phone || '',
        status: userData.status || 'active',
        createdAt: now,
        updatedAt: now,
        createdBy: user._id
      };

      // 創建用戶
      const result = await usersCollection.insertOne(userPayload);
      const newUser = await usersCollection.findOne(
        { _id: result.insertedId },
        { projection: { password: 0 } } // 不返回密碼
      );

      return {
        success: true,
        data: {
          ...newUser,
          temporaryPassword: userData.password ? null : password // 只有自動生成的密碼才返回
        },
        message: `用戶 "${userData.username}" 創建成功`
      };
    } catch (error) {
      console.error('創建用戶失敗:', error);
      return {
        success: false,
        error: error.message,
        message: '用戶創建失敗'
      };
    }
  }

  generateStoreId(storeName) {
    // 生成門店ID
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    
    // 清理門店名稱，只保留字母數字和中文
    const cleanName = storeName
      .replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '')
      .toLowerCase()
      .substring(0, 10); // 限制長度
    
    return `${cleanName}-${timestamp}-${randomSuffix}`;
  }

  generatePassword() {
    // 生成8位隨機密碼
    const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789';
    let password = '';
    for (let i = 0; i < 8; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  }

  // 解析螢幕尺寸描述 - 使用統一的screenConfigs
  parseScreenSize(description) {
    const sizeMap = getScreenSizeMap();

    // 嘗試從描述中提取尺寸
    for (const [size, resolution] of Object.entries(sizeMap)) {
      if (description.includes(size)) {
        return resolution;
      }
    }

    // 如果沒有匹配，返回默認值（2.9寸）
    return sizeMap['2.9'] || '128x296';
  }

  // 解析顏色類型描述
  parseColorType(description) {
    const colorMap = {
      '黑白': 'BW',           // 注意：在我們系統中BW=16級灰階
      '黑白紅': 'BWR',
      '黑白黃': 'BWY',
      '灰階': 'BW',           // 灰階對應BW
      '16級灰階': 'BW',       // 16級灰階對應BW
      '16灰階': 'BW'
    };

    // 嘗試從描述中提取顏色類型
    for (const [desc, type] of Object.entries(colorMap)) {
      if (description.includes(desc)) {
        return type;
      }
    }

    // 如果沒有匹配，返回默認值
    return 'BW';
  }

  // 解析方向描述
  parseOrientation(description) {
    if (description.includes('縱向') || description.includes('直向') || description.includes('portrait')) {
      return 'portrait';
    }
    return 'landscape'; // 默認橫向
  }

  // 處理模板類型邏輯
  async processTemplateType(normalizedData) {
    // 如果沒有指定模板類型，默認為系統模板
    if (!normalizedData.templateType) {
      normalizedData.templateType = 'system';
    }

    // 處理模板類型
    if (normalizedData.templateType === 'system') {
      // 系統模板
      normalizedData.isSystemTemplate = true;
      normalizedData.storeId = null;
    } else if (normalizedData.templateType === 'store') {
      // 門店模板
      normalizedData.isSystemTemplate = false;

      // 如果提供了門店名稱，需要查找門店ID
      if (normalizedData.storeName && !normalizedData.storeId) {
        const storeId = await this.findStoreIdByName(normalizedData.storeName);
        if (!storeId) {
          throw new Error(`找不到門店："${normalizedData.storeName}"`);
        }
        normalizedData.storeId = storeId;
      }

      // 如果沒有提供門店信息，拋出錯誤
      if (!normalizedData.storeId) {
        throw new Error('創建門店模板時必須指定門店名稱或門店ID');
      }
    } else {
      throw new Error(`無效的模板類型："${normalizedData.templateType}"，請使用 "system" 或 "store"`);
    }
  }

  // 根據門店名稱查找門店ID
  async findStoreIdByName(storeName) {
    try {
      if (!getDbConnection) {
        throw new Error('資料庫連接函數尚未初始化');
      }
      const { db } = await getDbConnection();
      const storesCollection = db.collection('stores');

      // 查找門店（支持模糊匹配）
      const store = await storesCollection.findOne({
        name: { $regex: storeName, $options: 'i' }
      });

      return store ? store.id : null;
    } catch (error) {
      console.error('查找門店失敗:', error);
      return null;
    }
  }

  // 智能顏色判定
  async smartColorDetection(normalizedData) {
    try {
      // 如果用戶已經指定了顏色，直接使用
      if (normalizedData.color) {
        return {
          selectedColor: normalizedData.color,
          needsUserInput: false,
          availableColors: []
        };
      }

      // 根據螢幕尺寸獲取支持的顏色
      const screenSize = normalizedData.screenSize;
      if (!screenSize) {
        // 如果沒有螢幕尺寸，使用默認顏色
        return {
          selectedColor: 'BW',
          needsUserInput: false,
          availableColors: []
        };
      }

      // 從螢幕尺寸中提取尺寸標識符
      const sizeIdentifier = this.extractSizeIdentifier(screenSize);
      const screenConfig = getScreenConfigBySize(sizeIdentifier);

      if (!screenConfig || !screenConfig.supportedColors) {
        // 如果找不到配置，使用默認顏色
        return {
          selectedColor: 'BW',
          needsUserInput: false,
          availableColors: []
        };
      }

      const supportedColors = screenConfig.supportedColors;
      const availableColorNames = this.mapDisplayColorTypeToNames(supportedColors);

      if (supportedColors.length === 1) {
        // 只有一種顏色，直接使用
        const selectedColor = this.mapDisplayColorTypeToCode(supportedColors[0]);
        console.log(`螢幕尺寸 ${sizeIdentifier} 只支持一種顏色，自動選擇: ${selectedColor}`);
        return {
          selectedColor: selectedColor,
          needsUserInput: false,
          availableColors: availableColorNames
        };
      } else {
        // 多種顏色可選，使用第一個作為默認值
        const selectedColor = this.mapDisplayColorTypeToCode(supportedColors[0]);
        console.log(`螢幕尺寸 ${sizeIdentifier} 支持多種顏色，自動選擇第一個: ${selectedColor}`);
        return {
          selectedColor: selectedColor,
          needsUserInput: false,
          availableColors: availableColorNames
        };
      }
    } catch (error) {
      console.error('智能顏色判定失敗:', error);
      return {
        selectedColor: 'BW',
        needsUserInput: false,
        availableColors: []
      };
    }
  }

  // 從螢幕尺寸字符串中提取尺寸標識符
  extractSizeIdentifier(screenSize) {
    // screenSize 格式如 "128x296" 或 "2.9寸"
    if (screenSize.includes('x')) {
      // 如果是分辨率格式，需要反向查找
      const sizeMap = getScreenSizeMap();
      for (const [size, resolution] of Object.entries(sizeMap)) {
        if (resolution === screenSize) {
          return size;
        }
      }
    } else {
      // 如果是描述格式，直接提取數字
      const match = screenSize.match(/(\d+\.?\d*)/);
      return match ? match[1] : '2.9';
    }
    return '2.9'; // 默認值
  }

  // 將DisplayColorType映射為顏色代碼
  mapDisplayColorTypeToCode(displayColorType) {
    const mapping = {
      [DisplayColorType.BW]: 'BW',        // BW = 16級灰階
      [DisplayColorType.BWR]: 'BWR',      // 黑白紅
      [DisplayColorType.BWRY]: 'BWY',     // 黑白紅黃 (注意：BWRY映射為BWY)
      [DisplayColorType.UNKNOWN]: 'BW'
    };
    return mapping[displayColorType] || 'BW';
  }

  // 將DisplayColorType映射為中文名稱
  mapDisplayColorTypeToNames(displayColorTypes) {
    const mapping = {
      [DisplayColorType.BW]: '16級灰階',     // BW = 16級灰階
      [DisplayColorType.BWR]: '黑白紅',
      [DisplayColorType.BWRY]: '黑白紅黃',
      [DisplayColorType.UNKNOWN]: '未知'
    };
    return displayColorTypes.map(type => mapping[type] || '未知');
  }
}

// 導出任務執行器實例和初始化函數
const taskExecutorInstance = new TaskExecutor();
module.exports = taskExecutorInstance;
module.exports.initDB = initDB;
