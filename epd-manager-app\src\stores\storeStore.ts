// EPD Manager App - 門店狀態管理

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Store, StoreState } from '../types';
import { apiService } from '../services/ApiService';

interface StoreStore extends StoreState {
  // Actions
  fetchStores: () => Promise<boolean>;
  selectStore: (store: Store) => void;
  clearSelectedStore: () => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Getters
  getStoreById: (storeId: string) => Store | undefined;
  hasSelectedStore: () => boolean;
}

export const useStoreStore = create<StoreStore>()(
  persist(
    (set, get) => ({
      // Initial state
      stores: [],
      selectedStore: null,
      loading: false,
      error: null,

      // Actions
      fetchStores: async () => {
        set({ loading: true, error: null });

        try {
          const result = await apiService.getStores();

          if (result.success && result.data) {
            set({
              stores: result.data,
              loading: false,
              error: null
            });
            return true;
          } else {
            set({
              loading: false,
              error: result.error || '獲取門店列表失敗'
            });
            return false;
          }
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || '獲取門店列表失敗'
          });
          return false;
        }
      },

      selectStore: (store: Store) => {
        set({ selectedStore: store });
        console.log('選擇門店:', store.name);
      },

      clearSelectedStore: () => {
        set({ selectedStore: null });
        console.log('清除選擇的門店');
      },

      clearError: () => {
        set({ error: null });
      },

      setLoading: (loading: boolean) => {
        set({ loading });
      },

      setError: (error: string | null) => {
        set({ error });
      },

      // Getters
      getStoreById: (storeId: string) => {
        const { stores } = get();
        return stores.find(store => store._id === storeId || store.id === storeId);
      },

      hasSelectedStore: () => {
        const { selectedStore } = get();
        return !!selectedStore;
      }
    }),
    {
      name: 'store-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        selectedStore: state.selectedStore,
        // 不持久化 stores 列表，每次重新獲取
      }),
      onRehydrateStorage: () => (state) => {
        // 重新加載時清除錯誤狀態
        if (state) {
          state.error = null;
          state.loading = false;
        }
      },
    }
  )
);

// 導出便捷的 hooks
export const useStores = () => {
  const store = useStoreStore();
  return {
    // State
    stores: store.stores,
    selectedStore: store.selectedStore,
    loading: store.loading,
    error: store.error,
    
    // Actions
    fetchStores: store.fetchStores,
    selectStore: store.selectStore,
    clearSelectedStore: store.clearSelectedStore,
    clearError: store.clearError,
    
    // Getters
    getStoreById: store.getStoreById,
    hasSelectedStore: store.hasSelectedStore(),
  };
};

export const useStoreActions = () => {
  const store = useStoreStore();
  return {
    fetchStores: store.fetchStores,
    selectStore: store.selectStore,
    clearSelectedStore: store.clearSelectedStore,
    clearError: store.clearError,
    setLoading: store.setLoading,
    setError: store.setError,
  };
};

export const useStoreState = () => {
  const store = useStoreStore();
  return {
    stores: store.stores,
    selectedStore: store.selectedStore,
    loading: store.loading,
    error: store.error,
    hasSelectedStore: store.hasSelectedStore(),
  };
};
