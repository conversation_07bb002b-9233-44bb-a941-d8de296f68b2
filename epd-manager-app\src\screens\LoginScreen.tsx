// EPD Manager App - 登入頁面

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from '../stores/authStore';
import { apiService } from '../services/ApiService';
import { COLORS, SIZES, DEFAULT_SETTINGS, API_CONFIG, STORAGE_KEYS } from '../utils/constants';
import { isValidIpAddress, buildServerUrl, extractIpFromUrl } from '../utils/generators';

export const LoginScreen: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [serverIp, setServerIp] = useState<string>(API_CONFIG.DEFAULT_HOST);
  const [rememberMe, setRememberMe] = useState(false);
  const [showServerConfig, setShowServerConfig] = useState(false);

  const { login, loading, error, clearError } = useAuth();

  useEffect(() => {
    // 清除之前的錯誤
    if (error) {
      clearError();
    }
  }, [username, password, serverIp]);

  // 組件掛載時載入保存的服務器 IP
  useEffect(() => {
    const loadSavedServerIp = async () => {
      try {
        const savedIp = await AsyncStorage.getItem(STORAGE_KEYS.SERVER_IP);
        if (savedIp && isValidIpAddress(savedIp)) {
          setServerIp(savedIp);
          // 設置 API 服務的基礎 URL
          const serverUrl = buildServerUrl(savedIp, API_CONFIG.DEFAULT_PORT, API_CONFIG.DEFAULT_PROTOCOL);
          apiService.setBaseURL(serverUrl);
        }
      } catch (error) {
        console.log('載入保存的服務器 IP 失敗:', error);
      }
    };

    loadSavedServerIp();
  }, []);

  const handleLogin = async () => {
    if (!username.trim()) {
      Alert.alert('錯誤', '請輸入用戶名');
      return;
    }

    if (!password.trim()) {
      Alert.alert('錯誤', '請輸入密碼');
      return;
    }

    // 驗證 IP 地址格式
    if (!isValidIpAddress(serverIp)) {
      Alert.alert('錯誤', '請輸入有效的 IP 地址');
      return;
    }

    // 構建完整的服務器 URL
    const serverUrl = buildServerUrl(serverIp, API_CONFIG.DEFAULT_PORT, API_CONFIG.DEFAULT_PROTOCOL);

    // 設置服務器地址
    if (serverUrl !== apiService.getBaseURL()) {
      apiService.setBaseURL(serverUrl);
    }

    try {
      const success = await login(username, password, rememberMe);

      if (success) {
        console.log('登入成功');
        // 保存服務器 IP 地址
        await AsyncStorage.setItem(STORAGE_KEYS.SERVER_IP, serverIp);
        // 登入成功後，App.tsx 會自動處理頁面跳轉
      } else {
        // 錯誤信息已經在 store 中設置
        if (error) {
          Alert.alert('登入失敗', error);
        }
      }
    } catch (err: any) {
      Alert.alert('登入失敗', err.message || '登入過程中發生錯誤');
    }
  };

  const testConnection = async () => {
    // 驗證 IP 地址格式
    if (!isValidIpAddress(serverIp)) {
      Alert.alert('錯誤', '請輸入有效的 IP 地址');
      return;
    }

    try {
      // 構建完整的服務器 URL
      const serverUrl = buildServerUrl(serverIp, API_CONFIG.DEFAULT_WEB_PORT, API_CONFIG.DEFAULT_PROTOCOL);
      apiService.setBaseURL(serverUrl);
      const result = await apiService.testConnection();

      if (result.success) {
        Alert.alert('連接成功', `服務器連接正常\n地址: ${serverIp}`);
      } else {
        Alert.alert('連接失敗', result.error || '無法連接到服務器');
      }
    } catch (error: any) {
      Alert.alert('連接失敗', error.message || '無法連接到服務器');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          {/* 應用標題 */}
          <View style={styles.header}>
            <Text style={styles.appTitle}>EPD Manager</Text>
          </View>

          {/* 登入表單 */}
          <View style={styles.form}>
            {/* 用戶名輸入 */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>用戶名</Text>
              <TextInput
                style={styles.input}
                value={username}
                onChangeText={setUsername}
                placeholder="請輸入用戶名"
                placeholderTextColor={COLORS.TEXT_DISABLED}
                autoCapitalize="none"
                autoCorrect={false}
                editable={!loading}
              />
            </View>

            {/* 密碼輸入 */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>密碼</Text>
              <TextInput
                style={styles.input}
                value={password}
                onChangeText={setPassword}
                placeholder="請輸入密碼"
                placeholderTextColor={COLORS.TEXT_DISABLED}
                secureTextEntry
                autoCapitalize="none"
                autoCorrect={false}
                editable={!loading}
              />
            </View>

            {/* 服務器配置 */}
            <TouchableOpacity
              style={styles.serverConfigToggle}
              onPress={() => setShowServerConfig(!showServerConfig)}
            >
              <Text style={styles.serverConfigToggleText}>
                {showServerConfig ? '隱藏' : '顯示'} 服務器設置
              </Text>
            </TouchableOpacity>

            {showServerConfig && (
              <View style={styles.serverConfigContainer}>
                <View style={styles.inputGroup}>
                  <Text style={styles.label}>服務器 IP 地址</Text>
                  <TextInput
                    style={styles.input}
                    value={serverIp}
                    onChangeText={setServerIp}
                    placeholder="*************"
                    placeholderTextColor={COLORS.TEXT_DISABLED}
                    autoCapitalize="none"
                    autoCorrect={false}
                    keyboardType="numeric"
                    editable={!loading}
                  />
                </View>

                <TouchableOpacity
                  style={styles.testButton}
                  onPress={testConnection}
                  disabled={loading}
                >
                  <Text style={styles.testButtonText}>測試連接</Text>
                </TouchableOpacity>
              </View>
            )}

            {/* 記住我選項 */}
            <TouchableOpacity
              style={styles.checkboxContainer}
              onPress={() => setRememberMe(!rememberMe)}
              disabled={loading}
            >
              <View style={[styles.checkbox, rememberMe && styles.checkboxChecked]}>
                {rememberMe && <Text style={styles.checkmark}>✓</Text>}
              </View>
              <Text style={styles.checkboxLabel}>記住登入狀態</Text>
            </TouchableOpacity>

            {/* 登入按鈕 */}
            <TouchableOpacity
              style={[styles.loginButton, loading && styles.loginButtonDisabled]}
              onPress={handleLogin}
              disabled={loading}
            >
              <Text style={styles.loginButtonText}>
                {loading ? '登入中...' : '登入'}
              </Text>
            </TouchableOpacity>

            {/* 錯誤信息 */}
            {error && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{error}</Text>
              </View>
            )}
          </View>

          {/* 底部信息 */}
          <View style={styles.footer}>
            <Text style={styles.footerText}>
              EPD Manager App v1.0.0
            </Text>
            <Text style={styles.footerText}>
              自動化網關配對解決方案
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: SIZES.SPACING_XL,
  },
  header: {
    alignItems: 'center',
    marginBottom: SIZES.SPACING_XL * 2,
  },
  appTitle: {
    fontSize: SIZES.FONT_SIZE_XXL * 1.5,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
    marginBottom: SIZES.SPACING_SM,
  },
  subtitle: {
    fontSize: SIZES.FONT_SIZE_MD,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  form: {
    marginBottom: SIZES.SPACING_XL,
  },
  inputGroup: {
    marginBottom: SIZES.SPACING_LG,
  },
  label: {
    fontSize: SIZES.FONT_SIZE_MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SIZES.SPACING_SM,
  },
  input: {
    backgroundColor: COLORS.SURFACE,
    borderWidth: 1,
    borderColor: COLORS.TEXT_DISABLED,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    paddingHorizontal: SIZES.SPACING_MD,
    paddingVertical: SIZES.SPACING_MD,
    fontSize: SIZES.FONT_SIZE_MD,
    color: COLORS.TEXT_PRIMARY,
  },
  serverConfigToggle: {
    alignItems: 'center',
    marginBottom: SIZES.SPACING_MD,
  },
  serverConfigToggleText: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.INFO,
    textDecorationLine: 'underline',
  },
  serverConfigContainer: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    padding: SIZES.SPACING_MD,
    marginBottom: SIZES.SPACING_LG,
    borderWidth: 1,
    borderColor: COLORS.TEXT_DISABLED,
  },
  helperText: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
    marginTop: SIZES.SPACING_XS,
    fontStyle: 'italic',
  },
  testButton: {
    backgroundColor: COLORS.INFO,
    paddingVertical: SIZES.SPACING_SM,
    paddingHorizontal: SIZES.SPACING_MD,
    borderRadius: SIZES.BORDER_RADIUS_SM,
    alignItems: 'center',
    marginTop: SIZES.SPACING_SM,
  },
  testButtonText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_SM,
    fontWeight: '600',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SIZES.SPACING_LG,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: COLORS.TEXT_DISABLED,
    borderRadius: 4,
    marginRight: SIZES.SPACING_SM,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxChecked: {
    backgroundColor: COLORS.PRIMARY,
    borderColor: COLORS.PRIMARY,
  },
  checkmark: {
    color: COLORS.SURFACE,
    fontSize: 12,
    fontWeight: 'bold',
  },
  checkboxLabel: {
    fontSize: SIZES.FONT_SIZE_MD,
    color: COLORS.TEXT_PRIMARY,
  },
  loginButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: SIZES.SPACING_LG,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    alignItems: 'center',
    marginBottom: SIZES.SPACING_MD,
  },
  loginButtonDisabled: {
    backgroundColor: COLORS.TEXT_DISABLED,
  },
  loginButtonText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_LG,
    fontWeight: 'bold',
  },
  errorContainer: {
    backgroundColor: COLORS.ERROR,
    padding: SIZES.SPACING_MD,
    borderRadius: SIZES.BORDER_RADIUS_SM,
    marginTop: SIZES.SPACING_SM,
  },
  errorText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_SM,
    textAlign: 'center',
  },
  footer: {
    alignItems: 'center',
    marginTop: SIZES.SPACING_XL,
  },
  footerText: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_DISABLED,
    textAlign: 'center',
    marginBottom: 4,
  },
});

export default LoginScreen;
