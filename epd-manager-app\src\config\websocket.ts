// EPD Manager App - WebSocket 配置

import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS, API_CONFIG } from '../utils/constants';

// WebSocket 服務器配置
export const WEBSOCKET_CONFIG = {
  // 默認配置
  DEFAULT: {
    HOST: API_CONFIG.DEFAULT_HOST, // 使用 API 配置中的默認主機
    PORT: '3001',
    PROTOCOL: 'ws'
  },

  // 連接配置
  CONNECTION: {
    RECONNECT_INTERVAL: 5000, // 重連間隔（毫秒）
    MAX_RECONNECT_ATTEMPTS: 5, // 最大重連次數
    CONNECTION_TIMEOUT: 10000, // 連接超時（毫秒）
    HEARTBEAT_INTERVAL: 30000, // 心跳間隔（毫秒）
  }
};

/**
 * 獲取用戶設定的服務器 IP
 * @returns Promise<string> 用戶設定的 IP 或默認 IP
 */
export const getUserServerIp = async (): Promise<string> => {
  try {
    const savedIp = await AsyncStorage.getItem(STORAGE_KEYS.SERVER_IP);
    if (savedIp) {
      console.log('使用用戶設定的服務器 IP:', savedIp);
      return savedIp;
    }
  } catch (error) {
    console.warn('獲取用戶設定的服務器 IP 失敗:', error);
  }

  console.log('使用默認服務器 IP:', WEBSOCKET_CONFIG.DEFAULT.HOST);
  return WEBSOCKET_CONFIG.DEFAULT.HOST;
};

/**
 * 獲取 WebSocket URL（異步版本，使用用戶設定的 IP）
 * @returns Promise<string> WebSocket URL
 */
export const getWebSocketUrl = async (): Promise<string> => {
  const serverIp = await getUserServerIp();
  const config = WEBSOCKET_CONFIG.DEFAULT;

  return `${config.PROTOCOL}://${serverIp}:${config.PORT}/ws`;
};

/**
 * 獲取前端 WebSocket URL（用於接收即時更新）
 * @returns Promise<string> 前端 WebSocket URL
 */
export const getFrontendWebSocketUrl = async (): Promise<string> => {
  return await getWebSocketUrl();
};

/**
 * 獲取網關 WebSocket URL（用於模擬網關）
 * @param storeId 門店 ID
 * @param gatewayId 網關 ID
 * @param token 認證 token
 * @returns Promise<string> 網關 WebSocket URL
 */
export const getGatewayWebSocketUrl = async (storeId: string, gatewayId: string, token: string): Promise<string> => {
  const baseUrl = (await getWebSocketUrl()).replace('/ws', '');
  return `${baseUrl}/store/${storeId}/gateway/${gatewayId}?token=${token}`;
};

/**
 * 同步版本的 WebSocket URL 獲取（用於初始化，可能使用默認值）
 * 注意：這個函數可能返回默認 IP，建議使用異步版本
 * @returns string WebSocket URL
 */
export const getWebSocketUrlSync = (): string => {
  const config = WEBSOCKET_CONFIG.DEFAULT;
  return `${config.PROTOCOL}://${config.HOST}:${config.PORT}/ws`;
};
