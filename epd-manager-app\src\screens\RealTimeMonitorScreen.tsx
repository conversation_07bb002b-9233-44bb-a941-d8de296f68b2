// EPD Manager App - 即時監控頁面

import React, { useEffect, useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Alert,
  Switch,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useStores } from '../stores/storeStore';
import {
  frontendWebSocketClient,
  subscribeToDeviceStatus,
  subscribeToGatewayStatus,
  subscribeToStoreDataUpdate,
  subscribeToSystemDataUpdate,
  subscribeToTemplateUpdate,
  subscribeToRefreshPlanUpdate,
  DeviceStatusEventHandler,
  GatewayStatusEventHandler,
  StoreDataUpdateEventHandler,
  SystemDataUpdateEventHandler,
  TemplateUpdateEventHandler,
  RefreshPlanUpdateEventHandler,
} from '../services/WebSocketService';
import { RealTimeStatsCard } from '../components/RealTimeStatsCard';
import { COLORS, SIZES } from '../utils/constants';

interface SubscriptionStatus {
  deviceStatus: boolean;
  gatewayStatus: boolean;
  storeDataUpdate: boolean;
  systemDataUpdate: boolean;
  templateUpdate: boolean;
  refreshPlanUpdate: boolean;
}

interface EventLog {
  id: string;
  timestamp: string;
  type: string;
  message: string;
  data?: any;
}

interface EventStats {
  deviceUpdates: number;
  gatewayUpdates: number;
  storeDataUpdates: number;
  systemDataUpdates: number;
  templateUpdates: number;
  refreshPlanUpdates: number;
  totalEvents: number;
}

export const RealTimeMonitorScreen: React.FC = () => {
  const [refreshing, setRefreshing] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [subscriptions, setSubscriptions] = useState<SubscriptionStatus>({
    deviceStatus: false,
    gatewayStatus: false,
    storeDataUpdate: false,
    systemDataUpdate: false,
    templateUpdate: false,
    refreshPlanUpdate: false,
  });
  const [eventLogs, setEventLogs] = useState<EventLog[]>([]);
  const [cleanupFunctions, setCleanupFunctions] = useState<Map<string, () => void>>(new Map());
  const [eventStats, setEventStats] = useState<EventStats>({
    deviceUpdates: 0,
    gatewayUpdates: 0,
    storeDataUpdates: 0,
    systemDataUpdates: 0,
    templateUpdates: 0,
    refreshPlanUpdates: 0,
    totalEvents: 0,
  });
  
  const { selectedStore } = useStores();

  // 添加事件日誌
  const addEventLog = useCallback((type: string, message: string, data?: any) => {
    const newLog: EventLog = {
      id: Date.now().toString(),
      timestamp: new Date().toLocaleTimeString(),
      type,
      message,
      data,
    };

    setEventLogs(prev => [newLog, ...prev.slice(0, 99)]); // 保留最新100條記錄

    // 更新統計數據
    setEventStats(prev => {
      const newStats = { ...prev };
      newStats.totalEvents += 1;

      switch (type) {
        case '設備狀態':
          newStats.deviceUpdates += 1;
          break;
        case '網關狀態':
          newStats.gatewayUpdates += 1;
          break;
        case '門店資料':
          newStats.storeDataUpdates += 1;
          break;
        case '系統資料':
          newStats.systemDataUpdates += 1;
          break;
        case '模板更新':
          newStats.templateUpdates += 1;
          break;
        case '刷圖計畫':
          newStats.refreshPlanUpdates += 1;
          break;
      }

      return newStats;
    });
  }, []);

  // 設備狀態事件處理器
  const deviceStatusHandler: DeviceStatusEventHandler = useCallback((event) => {
    addEventLog('設備狀態', `收到 ${event.devices?.length || 0} 個設備的狀態更新`, event);
  }, [addEventLog]);

  // 網關狀態事件處理器
  const gatewayStatusHandler: GatewayStatusEventHandler = useCallback((event) => {
    addEventLog('網關狀態', `收到 ${event.gateways?.length || 0} 個網關的狀態更新`, event);
  }, [addEventLog]);

  // 門店資料更新事件處理器
  const storeDataUpdateHandler: StoreDataUpdateEventHandler = useCallback((event) => {
    addEventLog('門店資料', `門店 ${event.storeId} 資料已更新`, event);
  }, [addEventLog]);

  // 系統資料更新事件處理器
  const systemDataUpdateHandler: SystemDataUpdateEventHandler = useCallback((event) => {
    addEventLog('系統資料', '系統資料已更新', event);
  }, [addEventLog]);

  // 模板更新事件處理器
  const templateUpdateHandler: TemplateUpdateEventHandler = useCallback((event) => {
    addEventLog('模板更新', `門店 ${event.storeId} 收到 ${event.templates?.length || 0} 個模板更新`, event);
  }, [addEventLog]);

  // 刷圖計畫更新事件處理器
  const refreshPlanUpdateHandler: RefreshPlanUpdateEventHandler = useCallback((event) => {
    addEventLog('刷圖計畫', `門店 ${event.storeId} 收到 ${event.refreshPlans?.length || 0} 個刷圖計畫更新`, event);
  }, [addEventLog]);

  // 初始化WebSocket連接
  useEffect(() => {
    const initializeConnection = async () => {
      try {
        const connected = await frontendWebSocketClient.connect();
        setIsConnected(connected);
        if (connected) {
          addEventLog('連接', 'WebSocket 連接已建立');
        } else {
          addEventLog('錯誤', 'WebSocket 連接失敗');
        }
      } catch (error) {
        console.error('WebSocket 連接錯誤:', error);
        addEventLog('錯誤', 'WebSocket 連接錯誤');
        setIsConnected(false);
      }
    };

    initializeConnection();

    // 定期檢查連接狀態
    const statusInterval = setInterval(() => {
      const connected = frontendWebSocketClient.getConnectionStatus();
      setIsConnected(connected);
    }, 5000);

    return () => {
      clearInterval(statusInterval);
      // 清理所有訂閱
      cleanupFunctions.forEach(cleanup => cleanup());
      frontendWebSocketClient.disconnect();
    };
  }, []);

  // 切換訂閱狀態
  const toggleSubscription = useCallback((type: keyof SubscriptionStatus) => {
    if (!selectedStore?.id) {
      Alert.alert('錯誤', '請先選擇門店');
      return;
    }

    const storeId = selectedStore.id;
    const isCurrentlySubscribed = subscriptions[type];
    
    if (isCurrentlySubscribed) {
      // 取消訂閱
      const cleanup = cleanupFunctions.get(type);
      if (cleanup) {
        cleanup();
        cleanupFunctions.delete(type);
        setCleanupFunctions(new Map(cleanupFunctions));
      }
      addEventLog('訂閱', `已取消訂閱 ${type}`);
    } else {
      // 開始訂閱
      let cleanup: (() => void) | null = null;
      
      switch (type) {
        case 'deviceStatus':
          cleanup = subscribeToDeviceStatus(storeId, deviceStatusHandler);
          break;
        case 'gatewayStatus':
          cleanup = subscribeToGatewayStatus(storeId, gatewayStatusHandler);
          break;
        case 'storeDataUpdate':
          cleanup = subscribeToStoreDataUpdate(storeId, storeDataUpdateHandler);
          break;
        case 'systemDataUpdate':
          cleanup = subscribeToSystemDataUpdate(systemDataUpdateHandler);
          break;
        case 'templateUpdate':
          cleanup = subscribeToTemplateUpdate(storeId, templateUpdateHandler);
          break;
        case 'refreshPlanUpdate':
          cleanup = subscribeToRefreshPlanUpdate(storeId, refreshPlanUpdateHandler);
          break;
      }
      
      if (cleanup) {
        cleanupFunctions.set(type, cleanup);
        setCleanupFunctions(new Map(cleanupFunctions));
        addEventLog('訂閱', `已開始訂閱 ${type}`);
      }
    }
    
    setSubscriptions(prev => ({
      ...prev,
      [type]: !isCurrentlySubscribed,
    }));
  }, [selectedStore, subscriptions, cleanupFunctions, deviceStatusHandler, gatewayStatusHandler, storeDataUpdateHandler, systemDataUpdateHandler, templateUpdateHandler, refreshPlanUpdateHandler, addEventLog]);

  const handleRefresh = async () => {
    setRefreshing(true);
    // 重新連接WebSocket
    try {
      const connected = await frontendWebSocketClient.connect();
      setIsConnected(connected);
      if (connected) {
        addEventLog('連接', 'WebSocket 重新連接成功');
      }
    } catch (error) {
      addEventLog('錯誤', 'WebSocket 重新連接失敗');
    }
    setRefreshing(false);
  };

  const clearLogs = () => {
    setEventLogs([]);
    setEventStats({
      deviceUpdates: 0,
      gatewayUpdates: 0,
      storeDataUpdates: 0,
      systemDataUpdates: 0,
      templateUpdates: 0,
      refreshPlanUpdates: 0,
      totalEvents: 0,
    });
    addEventLog('系統', '事件日誌已清空');
  };

  const getSubscriptionDisplayName = (type: keyof SubscriptionStatus): string => {
    const names = {
      deviceStatus: '設備狀態',
      gatewayStatus: '網關狀態',
      storeDataUpdate: '門店資料',
      systemDataUpdate: '系統資料',
      templateUpdate: '模板更新',
      refreshPlanUpdate: '刷圖計畫',
    };
    return names[type];
  };

  const getSubscriptionIcon = (type: keyof SubscriptionStatus): string => {
    const icons = {
      deviceStatus: '📱',
      gatewayStatus: '🌐',
      storeDataUpdate: '🏪',
      systemDataUpdate: '⚙️',
      templateUpdate: '📄',
      refreshPlanUpdate: '🖼️',
    };
    return icons[type];
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[COLORS.PRIMARY]}
          />
        }
      >
        {/* 連接狀態 */}
        <View style={styles.statusCard}>
          <View style={styles.statusHeader}>
            <Text style={styles.statusTitle}>WebSocket 連接狀態</Text>
            <View style={[
              styles.statusIndicator,
              { backgroundColor: isConnected ? COLORS.SUCCESS : COLORS.ERROR }
            ]} />
          </View>
          <Text style={styles.statusText}>
            {isConnected ? '已連接' : '未連接'}
          </Text>
          {selectedStore && (
            <Text style={styles.storeInfo}>
              當前門店：{selectedStore.name}
            </Text>
          )}
        </View>

        {/* 即時統計 */}
        <RealTimeStatsCard
          title="📊 即時統計"
          stats={[
            {
              label: '總事件數',
              value: eventStats.totalEvents,
              icon: '📈',
              color: COLORS.PRIMARY,
              trend: eventStats.totalEvents > 0 ? 'up' : 'stable',
            },
            {
              label: '設備更新',
              value: eventStats.deviceUpdates,
              icon: '📱',
              color: COLORS.SUCCESS,
            },
            {
              label: '網關更新',
              value: eventStats.gatewayUpdates,
              icon: '🌐',
              color: COLORS.INFO,
            },
            {
              label: '門店資料',
              value: eventStats.storeDataUpdates,
              icon: '🏪',
              color: COLORS.WARNING,
            },
          ]}
        />

        <RealTimeStatsCard
          title="🔄 系統更新統計"
          stats={[
            {
              label: '系統資料',
              value: eventStats.systemDataUpdates,
              icon: '⚙️',
              color: COLORS.ERROR,
            },
            {
              label: '模板更新',
              value: eventStats.templateUpdates,
              icon: '📄',
              color: COLORS.SUCCESS,
            },
            {
              label: '刷圖計畫',
              value: eventStats.refreshPlanUpdates,
              icon: '🖼️',
              color: COLORS.INFO,
            },
            {
              label: '活躍訂閱',
              value: Object.values(subscriptions).filter(Boolean).length,
              icon: '🔔',
              color: COLORS.PRIMARY,
              trend: Object.values(subscriptions).filter(Boolean).length > 0 ? 'up' : 'stable',
            },
          ]}
        />

        {/* 訂閱控制 */}
        <View style={styles.subscriptionCard}>
          <View style={styles.subscriptionHeader}>
            <Text style={styles.sectionTitle}>🔔 即時訂閱控制</Text>
            <View style={styles.subscriptionSummary}>
              <Text style={styles.subscriptionSummaryText}>
                {Object.values(subscriptions).filter(Boolean).length} / {Object.keys(subscriptions).length} 已啟用
              </Text>
            </View>
          </View>
          <Text style={styles.sectionDescription}>
            選擇要監控的即時更新類型，獲得最新的系統狀態
          </Text>

          <View style={styles.subscriptionGrid}>
            {(Object.keys(subscriptions) as Array<keyof SubscriptionStatus>).map((type) => (
              <TouchableOpacity
                key={type}
                style={[
                  styles.subscriptionItem,
                  subscriptions[type] && styles.subscriptionItemActive,
                  (!isConnected || !selectedStore) && styles.subscriptionItemDisabled,
                ]}
                onPress={() => toggleSubscription(type)}
                disabled={!isConnected || !selectedStore}
                activeOpacity={0.7}
              >
                <View style={styles.subscriptionIcon}>
                  <Text style={styles.subscriptionIconText}>
                    {getSubscriptionIcon(type)}
                  </Text>
                </View>
                <Text style={[
                  styles.subscriptionName,
                  subscriptions[type] && styles.subscriptionNameActive,
                ]}>
                  {getSubscriptionDisplayName(type)}
                </Text>
                <Text style={[
                  styles.subscriptionStatus,
                  subscriptions[type] && styles.subscriptionStatusActive,
                ]}>
                  {subscriptions[type] ? '✅ 已訂閱' : '⭕ 未訂閱'}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* 事件日誌 */}
        <View style={styles.logsCard}>
          <View style={styles.logsHeader}>
            <Text style={styles.sectionTitle}>事件日誌</Text>
            <TouchableOpacity
              style={styles.clearButton}
              onPress={clearLogs}
            >
              <Text style={styles.clearButtonText}>清空</Text>
            </TouchableOpacity>
          </View>
          
          {eventLogs.length === 0 ? (
            <Text style={styles.noLogsText}>暫無事件記錄</Text>
          ) : (
            eventLogs.map((log) => (
              <View key={log.id} style={styles.logItem}>
                <View style={styles.logHeader}>
                  <Text style={styles.logType}>{log.type}</Text>
                  <Text style={styles.logTime}>{log.timestamp}</Text>
                </View>
                <Text style={styles.logMessage}>{log.message}</Text>
              </View>
            ))
          )}
        </View>

        {/* 底部間距 */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: SIZES.SPACING_MD,
  },
  statusCard: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    padding: SIZES.SPACING_MD,
    marginBottom: SIZES.SPACING_MD,
    borderWidth: 1,
    borderColor: COLORS.TEXT_DISABLED,
  },
  statusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.SPACING_SM,
  },
  statusTitle: {
    fontSize: SIZES.FONT_SIZE_MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  statusText: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SIZES.SPACING_XS,
  },
  storeInfo: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.INFO,
    fontWeight: '500',
  },
  subscriptionCard: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    padding: SIZES.SPACING_MD,
    marginBottom: SIZES.SPACING_MD,
    borderWidth: 1,
    borderColor: COLORS.TEXT_DISABLED,
    shadowColor: COLORS.TEXT_PRIMARY,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  subscriptionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.SPACING_XS,
  },
  subscriptionSummary: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: SIZES.SPACING_SM,
    paddingVertical: SIZES.SPACING_XS,
    borderRadius: SIZES.BORDER_RADIUS_SM,
  },
  subscriptionSummaryText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_XS,
    fontWeight: '600',
  },
  sectionTitle: {
    fontSize: SIZES.FONT_SIZE_LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  sectionDescription: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SIZES.SPACING_MD,
    lineHeight: 20,
  },
  subscriptionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  subscriptionItem: {
    width: '48%',
    backgroundColor: COLORS.BACKGROUND,
    borderRadius: SIZES.BORDER_RADIUS_SM,
    padding: SIZES.SPACING_SM,
    marginBottom: SIZES.SPACING_SM,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: COLORS.TEXT_DISABLED,
  },
  subscriptionItemActive: {
    borderColor: COLORS.PRIMARY,
    backgroundColor: `${COLORS.PRIMARY}10`,
  },
  subscriptionItemDisabled: {
    opacity: 0.5,
  },
  subscriptionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.SURFACE,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SIZES.SPACING_XS,
    borderWidth: 1,
    borderColor: COLORS.TEXT_DISABLED,
  },
  subscriptionIconText: {
    fontSize: SIZES.FONT_SIZE_LG,
  },
  subscriptionName: {
    fontSize: SIZES.FONT_SIZE_SM,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SIZES.SPACING_XS,
    textAlign: 'center',
  },
  subscriptionNameActive: {
    color: COLORS.PRIMARY,
  },
  subscriptionStatus: {
    fontSize: SIZES.FONT_SIZE_XS,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  subscriptionStatusActive: {
    color: COLORS.SUCCESS,
    fontWeight: '600',
  },
  logsCard: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    padding: SIZES.SPACING_MD,
    marginBottom: SIZES.SPACING_MD,
    borderWidth: 1,
    borderColor: COLORS.TEXT_DISABLED,
  },
  logsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.SPACING_MD,
  },
  clearButton: {
    backgroundColor: COLORS.WARNING,
    paddingHorizontal: SIZES.SPACING_SM,
    paddingVertical: SIZES.SPACING_XS,
    borderRadius: SIZES.BORDER_RADIUS_SM,
  },
  clearButtonText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_XS,
    fontWeight: '600',
  },
  noLogsText: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    paddingVertical: SIZES.SPACING_LG,
    fontStyle: 'italic',
  },
  logItem: {
    backgroundColor: COLORS.BACKGROUND,
    borderRadius: SIZES.BORDER_RADIUS_SM,
    padding: SIZES.SPACING_SM,
    marginBottom: SIZES.SPACING_XS,
    borderLeftWidth: 3,
    borderLeftColor: COLORS.INFO,
  },
  logHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.SPACING_XS,
  },
  logType: {
    fontSize: SIZES.FONT_SIZE_SM,
    fontWeight: '600',
    color: COLORS.PRIMARY,
  },
  logTime: {
    fontSize: SIZES.FONT_SIZE_XS,
    color: COLORS.TEXT_SECONDARY,
    fontFamily: 'monospace',
  },
  logMessage: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_PRIMARY,
    lineHeight: 18,
  },
  bottomSpacing: {
    height: SIZES.SPACING_XL,
  },
});

export default RealTimeMonitorScreen;
