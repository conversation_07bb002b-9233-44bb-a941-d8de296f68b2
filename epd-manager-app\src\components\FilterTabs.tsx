import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { COLORS, SIZES } from '../utils/constants';

export interface FilterOption {
  key: string;
  label: string;
  count?: number;
}

interface FilterTabsProps {
  options: FilterOption[];
  selectedKey: string;
  onSelect: (key: string) => void;
  style?: any;
}

export const FilterTabs: React.FC<FilterTabsProps> = ({
  options,
  selectedKey,
  onSelect,
  style
}) => {
  return (
    <View style={[styles.container, style]}>
      {options.map((option) => (
        <TouchableOpacity
          key={option.key}
          style={[
            styles.tab,
            selectedKey === option.key && styles.activeTab
          ]}
          onPress={() => onSelect(option.key)}
          activeOpacity={0.7}
        >
          <Text style={[
            styles.tabText,
            selectedKey === option.key && styles.activeTabText
          ]}>
            {option.label}
            {option.count !== undefined && (
              <Text style={[
                styles.countText,
                selectedKey === option.key && styles.activeCountText
              ]}>
                {' '}({option.count})
              </Text>
            )}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: COLORS.BACKGROUND,
    paddingHorizontal: SIZES.SPACING_MD,
    paddingVertical: SIZES.SPACING_SM,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  tab: {
    paddingHorizontal: SIZES.SPACING_MD,
    paddingVertical: SIZES.SPACING_SM,
    marginRight: SIZES.SPACING_SM,
    borderRadius: SIZES.BORDER_RADIUS_LG,
    backgroundColor: COLORS.CARD_BACKGROUND,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
  },
  activeTab: {
    backgroundColor: COLORS.PRIMARY,
    borderColor: COLORS.PRIMARY,
  },
  tabText: {
    fontSize: SIZES.FONT_SIZE_SM,
    fontWeight: '500',
    color: COLORS.TEXT_PRIMARY,
  },
  activeTabText: {
    color: COLORS.WHITE,
  },
  countText: {
    fontSize: SIZES.FONT_SIZE_XS,
    fontWeight: '400',
    color: COLORS.TEXT_SECONDARY,
  },
  activeCountText: {
    color: COLORS.WHITE,
    opacity: 0.8,
  },
});
