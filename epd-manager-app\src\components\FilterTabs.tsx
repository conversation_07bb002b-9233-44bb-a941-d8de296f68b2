import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { COLORS } from '../utils/constants';

export interface FilterOption {
  key: string;
  label: string;
  count?: number;
}

interface FilterTabsProps {
  options: FilterOption[];
  selectedFilter: string;
  onFilterChange: (filter: string) => void;
  style?: any;
}

export const FilterTabs: React.FC<FilterTabsProps> = ({
  options,
  selectedFilter,
  onFilterChange,
  style
}) => {
  return (
    <View style={[styles.container, style]}>
      {options.map((option) => (
        <TouchableOpacity
          key={option.key}
          style={[
            styles.tab,
            selectedFilter === option.key && styles.activeTab
          ]}
          onPress={() => onFilterChange(option.key)}
          activeOpacity={0.7}
        >
          <Text style={[
            styles.tabText,
            selectedFilter === option.key && styles.activeTabText
          ]}>
            {option.label}
            {option.count !== undefined && (
              <Text style={[
                styles.countText,
                selectedFilter === option.key && styles.activeCountText
              ]}>
                {' '}({option.count})
              </Text>
            )}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: COLORS.BACKGROUND,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  tab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: COLORS.CARD_BACKGROUND,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
  },
  activeTab: {
    backgroundColor: COLORS.PRIMARY,
    borderColor: COLORS.PRIMARY,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.TEXT_PRIMARY,
  },
  activeTabText: {
    color: COLORS.WHITE,
  },
  countText: {
    fontSize: 12,
    fontWeight: '400',
    color: COLORS.TEXT_SECONDARY,
  },
  activeCountText: {
    color: COLORS.WHITE,
    opacity: 0.8,
  },
});
