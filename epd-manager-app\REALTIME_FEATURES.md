# EPD Manager App - 即時監控功能

## 🚀 新增功能概覽

EPD Manager App 現在支援全面的即時監控功能，透過 WebSocket 連接提供實時數據更新。

## 📡 支援的即時訂閱類型

### 1. 設備狀態訂閱 (Device Status)
- **功能**: 即時監控 EPD 設備的狀態變化
- **數據包含**: 設備在線狀態、電池電量、信號強度、圖像狀態
- **圖標**: 📱
- **用途**: 設備管理、故障診斷

### 2. 網關狀態訂閱 (Gateway Status)
- **功能**: 即時監控網關設備的連接狀態
- **數據包含**: 網關在線狀態、連接信息、韌體版本
- **圖標**: 🌐
- **用途**: 網關管理、連接監控

### 3. 門店資料更新訂閱 (Store Data Update)
- **功能**: 監控門店相關數據的變更
- **數據包含**: 門店配置、設備列表、模板分配
- **圖標**: 🏪
- **用途**: 門店管理、配置同步

### 4. 系統資料更新訂閱 (System Data Update)
- **功能**: 監控系統級別的配置變更
- **數據包含**: 系統設定、全局配置、權限變更
- **圖標**: ⚙️
- **用途**: 系統管理、配置監控

### 5. 模板更新訂閱 (Template Update)
- **功能**: 即時接收模板的新增、修改、刪除通知
- **數據包含**: 模板內容、版本信息、分配狀態
- **圖標**: 📄
- **用途**: 內容管理、模板同步

### 6. 刷圖計畫更新訂閱 (Refresh Plan Update)
- **功能**: 監控刷圖計畫的執行狀態
- **數據包含**: 計畫進度、執行結果、錯誤信息
- **圖標**: 🖼️
- **用途**: 任務監控、進度追蹤

## 🎨 用戶界面特色

### 即時統計儀表板
- **總事件數**: 顯示接收到的所有事件總數
- **分類統計**: 按事件類型分別統計
- **趨勢指示**: 使用圖標和顏色顯示數據趨勢
- **活躍訂閱**: 顯示當前啟用的訂閱數量

### 智能訂閱控制
- **視覺化開關**: 使用卡片式設計，直觀顯示訂閱狀態
- **圖標識別**: 每種訂閱類型都有專屬圖標
- **狀態指示**: 清楚顯示已訂閱/未訂閱狀態
- **批量管理**: 支援快速啟用/停用多個訂閱

### 事件日誌系統
- **即時記錄**: 所有事件都會即時記錄到日誌中
- **時間戳記**: 精確記錄事件發生時間
- **事件分類**: 按類型分類顯示事件
- **自動清理**: 保留最新 100 條記錄，自動清理舊記錄

## 🔧 技術實現

### WebSocket 連接管理
- **自動重連**: 連接斷開時自動嘗試重連
- **心跳檢測**: 定期檢查連接狀態
- **錯誤處理**: 完善的錯誤處理和用戶提示

### 訂閱生命週期管理
- **動態訂閱**: 支援運行時動態添加/移除訂閱
- **資源清理**: 頁面關閉時自動清理所有訂閱
- **狀態同步**: 訂閱狀態與 UI 狀態保持同步

### 性能優化
- **事件節流**: 避免過於頻繁的 UI 更新
- **記憶體管理**: 限制日誌記錄數量，防止記憶體洩漏
- **批量更新**: 使用 React 的批量更新機制提升性能

## 📱 使用方法

### 1. 開啟即時監控
1. 在主控台頁面點擊「即時監控」按鈕
2. 確保 WebSocket 連接狀態顯示為「已連接」
3. 確認已選擇正確的門店

### 2. 啟用訂閱
1. 在「即時訂閱控制」區域選擇要監控的類型
2. 點擊對應的卡片來啟用/停用訂閱
3. 觀察統計數據的變化

### 3. 查看事件日誌
1. 在「事件日誌」區域查看即時事件
2. 每個事件都包含時間、類型和詳細信息
3. 可以點擊「清空」按鈕清理日誌

### 4. 監控統計數據
1. 在統計卡片中查看各類事件的數量
2. 觀察趨勢指示器了解數據變化
3. 使用統計數據進行系統分析

## 🎯 應用場景

### 運營監控
- 即時監控所有門店的設備狀態
- 快速發現和響應設備故障
- 追蹤系統性能和穩定性

### 內容管理
- 監控模板更新的推送狀態
- 確保內容及時同步到所有設備
- 追蹤刷圖計畫的執行進度

### 系統維護
- 監控系統配置變更
- 即時接收系統告警
- 追蹤系統資源使用情況

## 🔮 未來擴展

- **批量進度監控**: 支援批量操作的進度追蹤
- **自定義告警**: 支援設定自定義告警規則
- **數據導出**: 支援將監控數據導出為報表
- **歷史數據**: 支援查看歷史監控數據

## 💡 使用建議

1. **選擇性訂閱**: 根據實際需求選擇訂閱類型，避免不必要的資源消耗
2. **定期清理**: 定期清理事件日誌，保持界面整潔
3. **網絡穩定**: 確保網絡連接穩定，以獲得最佳的即時監控體驗
4. **權限管理**: 確保用戶具有相應的權限來訂閱特定類型的事件

---

*此功能為 EPD Manager App 的核心功能之一，提供了全面的即時監控能力，幫助用戶更好地管理和監控 EPD 系統。*
