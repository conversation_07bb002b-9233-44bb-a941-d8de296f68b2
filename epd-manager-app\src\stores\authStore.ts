// EPD Manager App - 認證狀態管理

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { User, AuthState } from '../types';
import { apiService } from '../services/ApiService';
import { STORAGE_KEYS, SUCCESS_MESSAGES, ERROR_MESSAGES } from '../utils/constants';

interface AuthStore extends AuthState {
  // Actions
  login: (username: string, password: string, rememberMe?: boolean) => Promise<boolean>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<boolean>;
  clearError: () => void;
  setUser: (user: User | null) => void;
  setToken: (token: string | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Getters
  isLoggedIn: () => boolean;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      isAuthenticated: false,
      user: null,
      token: null,
      loading: false,
      error: null,

      // Actions
      login: async (username: string, password: string, rememberMe: boolean = false) => {
        set({ loading: true, error: null });

        try {
          const result = await apiService.login(username, password, rememberMe);

          if (result.success && result.data) {
            const { user, token } = result.data;
            
            set({
              isAuthenticated: true,
              user,
              token,
              loading: false,
              error: null
            });

            console.log(SUCCESS_MESSAGES.LOGIN_SUCCESS);
            return true;
          } else {
            set({
              loading: false,
              error: result.error || ERROR_MESSAGES.AUTH_FAILED
            });
            return false;
          }
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || ERROR_MESSAGES.AUTH_FAILED
          });
          return false;
        }
      },

      logout: async () => {
        set({ loading: true });

        try {
          await apiService.logout();
        } catch (error) {
          console.warn('服務器端登出失敗，但繼續清除本地狀態');
        }

        // 清除本地狀態
        set({
          isAuthenticated: false,
          user: null,
          token: null,
          loading: false,
          error: null
        });

        console.log(SUCCESS_MESSAGES.LOGOUT_SUCCESS);
      },

      checkAuth: async () => {
        const { token } = get();
        
        if (!token) {
          return false;
        }

        set({ loading: true });

        try {
          const result = await apiService.checkAuth();

          if (result.success && result.data) {
            set({
              isAuthenticated: true,
              user: result.data,
              loading: false,
              error: null
            });
            return true;
          } else {
            // Token 無效，清除狀態
            set({
              isAuthenticated: false,
              user: null,
              token: null,
              loading: false,
              error: null
            });
            return false;
          }
        } catch (error: any) {
          set({
            isAuthenticated: false,
            user: null,
            token: null,
            loading: false,
            error: error.message
          });
          return false;
        }
      },

      clearError: () => {
        set({ error: null });
      },

      setUser: (user: User | null) => {
        set({ user });
      },

      setToken: (token: string | null) => {
        set({ token });
      },

      setLoading: (loading: boolean) => {
        set({ loading });
      },

      setError: (error: string | null) => {
        set({ error });
      },

      // Getters
      isLoggedIn: () => {
        const { isAuthenticated, token } = get();
        return isAuthenticated && !!token;
      },

      hasPermission: (permission: string) => {
        const { user } = get();
        if (!user || !user.permissions) {
          return false;
        }
        return user.permissions.includes(permission) || user.permissions.includes('*');
      },

      hasRole: (role: string) => {
        const { user } = get();
        if (!user || !user.roles) {
          return false;
        }
        return user.roles.includes(role) || user.roles.includes('admin');
      }
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        token: state.token,
        // 不持久化 loading 和 error 狀態
      }),
      onRehydrateStorage: () => (state) => {
        // 重新加載時清除錯誤狀態
        if (state) {
          state.error = null;
          state.loading = false;
        }
      },
    }
  )
);

// 導出便捷的 hooks
export const useAuth = () => {
  const store = useAuthStore();
  return {
    // State
    isAuthenticated: store.isAuthenticated,
    user: store.user,
    token: store.token,
    loading: store.loading,
    error: store.error,
    
    // Actions
    checkAuth: store.checkAuth,
    login: store.login,
    logout: store.logout,
    clearError: store.clearError,
    
    // Getters
    isLoggedIn: store.isLoggedIn(),
    hasPermission: store.hasPermission,
    hasRole: store.hasRole,
  };
};

export const useAuthActions = () => {
  const store = useAuthStore();
  return {
    login: store.login,
    logout: store.logout,
    checkAuth: store.checkAuth,
    clearError: store.clearError,
    setUser: store.setUser,
    setToken: store.setToken,
    setLoading: store.setLoading,
    setError: store.setError,
  };
};



export const useAuthState = () => {
  const store = useAuthStore();
  return {
    isAuthenticated: store.isAuthenticated,
    user: store.user,
    token: store.token,
    loading: store.loading,
    error: store.error,
    isLoggedIn: store.isLoggedIn(),
  };
};
