// EPD Manager App - API 服務

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_CONFIG, STORAGE_KEYS, ERROR_MESSAGES } from '../utils/constants';
import { ApiResponse, User, Store, Gateway, GatewayConfig, Device, Template } from '../types';

class ApiService {
  private axiosInstance: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = `${API_CONFIG.DEFAULT_PROTOCOL}://${API_CONFIG.DEFAULT_HOST}:${API_CONFIG.DEFAULT_PORT}`;
    this.axiosInstance = this.createAxiosInstance();
  }

  /**
   * 創建 Axios 實例
   */
  private createAxiosInstance(): AxiosInstance {
    const instance = axios.create({
      baseURL: this.baseURL,
      timeout: API_CONFIG.TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 請求攔截器 - 添加認證 token
    instance.interceptors.request.use(
      async (config) => {
        const token = await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
          // 同時設置 Cookie（與主專案保持一致）
          config.headers.Cookie = `token=${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 回應攔截器 - 處理錯誤
    instance.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      async (error) => {
        if (error.response?.status === 401) {
          // Token 過期，清除本地存儲
          await this.clearAuthData();
          throw new Error(ERROR_MESSAGES.TOKEN_EXPIRED);
        }
        
        if (error.code === 'NETWORK_ERROR' || !error.response) {
          throw new Error(ERROR_MESSAGES.NETWORK_ERROR);
        }
        
        const errorMessage = error.response?.data?.error || ERROR_MESSAGES.SERVER_ERROR;
        throw new Error(errorMessage);
      }
    );

    return instance;
  }

  /**
   * 設置基礎 URL
   */
  setBaseURL(url: string): void {
    this.baseURL = url;
    this.axiosInstance.defaults.baseURL = url;
  }

  /**
   * 清除認證數據
   */
  private async clearAuthData(): Promise<void> {
    await AsyncStorage.multiRemove([
      STORAGE_KEYS.AUTH_TOKEN,
      STORAGE_KEYS.USER_INFO,
    ]);
  }

  // ==================== 認證相關 API ====================

  /**
   * 用戶登入
   */
  async login(username: string, password: string, rememberMe: boolean = false): Promise<ApiResponse<{ user: User; token: string }>> {
    try {
      const response = await this.axiosInstance.post('/api/auth/login', {
        username,
        password,
        rememberMe,
      });

      const { user, token } = response.data;

      // 保存認證信息
      await AsyncStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);
      await AsyncStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(user));
      
      if (rememberMe) {
        await AsyncStorage.setItem(STORAGE_KEYS.REMEMBER_LOGIN, 'true');
      }

      return {
        success: true,
        data: { user, token },
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 用戶登出
   */
  async logout(): Promise<ApiResponse> {
    try {
      await this.axiosInstance.post('/api/auth/logout');
      await this.clearAuthData();
      
      return {
        success: true,
      };
    } catch (error: any) {
      // 即使服務器端登出失敗，也要清除本地數據
      await this.clearAuthData();
      return {
        success: true,
      };
    }
  }

  /**
   * 檢查認證狀態
   */
  async checkAuth(): Promise<ApiResponse<User>> {
    try {
      const response = await this.axiosInstance.get('/api/auth/me');
      return {
        success: true,
        data: response.data.user,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // ==================== 門店相關 API ====================

  /**
   * 獲取所有門店
   */
  async getStores(): Promise<ApiResponse<Store[]>> {
    try {
      const response = await this.axiosInstance.get('/api/stores');
      
      // 處理不同的回應格式
      let stores: Store[] = [];
      if (Array.isArray(response.data)) {
        stores = response.data;
      } else if (response.data.stores && Array.isArray(response.data.stores)) {
        stores = response.data.stores;
      } else if (response.data.data && Array.isArray(response.data.data)) {
        stores = response.data.data;
      }

      return {
        success: true,
        data: stores,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // ==================== 網關相關 API ====================

  /**
   * 獲取門店的網關列表
   */
  async getGateways(storeId: string): Promise<ApiResponse<Gateway[]>> {
    try {
      const response = await this.axiosInstance.get(`/api/gateways?storeId=${storeId}`);

      // 處理不同的回應格式，與 test-ws-client-interactive.js 保持一致
      let gateways: Gateway[] = [];
      if (Array.isArray(response.data)) {
        // 如果響應直接是數組
        gateways = response.data;
      } else if (response.data && typeof response.data === 'object') {
        // 如果響應是對象，嘗試獲取 gateways 屬性
        if (Array.isArray(response.data.gateways)) {
          gateways = response.data.gateways;
        } else if (response.data.data && Array.isArray(response.data.data)) {
          gateways = response.data.data;
        }
      }

      return {
        success: true,
        data: gateways,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 創建新網關
   */
  async createGateway(gatewayConfig: GatewayConfig): Promise<ApiResponse<Gateway>> {
    try {
      const response = await this.axiosInstance.post('/api/gateways', gatewayConfig);
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 獲取單個網關詳情
   */
  async getGateway(gatewayId: string, storeId?: string): Promise<ApiResponse<Gateway>> {
    try {
      let url = `/gateways/${gatewayId}`;
      if (storeId) {
        url += `?storeId=${storeId}`;
      }
      
      const response = await this.axiosInstance.get(url);
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 更新網關信息
   */
  async updateGateway(gatewayId: string, updates: Partial<Gateway>): Promise<ApiResponse<Gateway>> {
    try {
      const response = await this.axiosInstance.put(`/gateways/${gatewayId}`, updates);
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 刪除網關
   */
  async deleteGateway(gatewayId: string): Promise<ApiResponse> {
    try {
      await this.axiosInstance.delete(`/gateways/${gatewayId}`);
      return {
        success: true,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // ==================== 設備相關 API ====================

  /**
   * 獲取門店的設備列表
   */
  async getDevices(storeId: string): Promise<ApiResponse<Device[]>> {
    try {
      const response = await this.axiosInstance.get(`/api/devices?storeId=${storeId}`);
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 獲取單個設備詳情
   */
  async getDevice(deviceId: string, storeId?: string): Promise<ApiResponse<Device>> {
    try {
      let url = `/api/devices/${deviceId}`;
      if (storeId) {
        url += `?storeId=${storeId}`;
      }

      const response = await this.axiosInstance.get(url);
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 創建新設備
   */
  async createDevice(deviceData: Partial<Device>): Promise<ApiResponse<Device>> {
    try {
      const response = await this.axiosInstance.post('/api/devices', deviceData);
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 同步設備狀態
   */
  async syncDevices(storeId?: string): Promise<ApiResponse> {
    try {
      let url = '/api/devices/sync';
      if (storeId) {
        url += `?storeId=${storeId}`;
      }

      const response = await this.axiosInstance.post(url);
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 發送設備預覽圖到網關
   */
  async sendDevicePreviewToGateway(
    deviceId: string,
    options: {
      sendToAllGateways?: boolean;
      storeId?: string;
    } = {}
  ): Promise<ApiResponse> {
    try {
      const { sendToAllGateways = false, storeId } = options;

      // 構建 URL
      let url = `/api/devices/${deviceId}/send-preview`;
      if (storeId) {
        url += `?storeId=${encodeURIComponent(storeId)}`;
      }

      const response = await this.axiosInstance.post(url, {
        sendToAllGateways,
      });

      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // ==================== 模板相關 API ====================

  /**
   * 獲取門店的模板列表
   */
  async getTemplates(storeId?: string): Promise<ApiResponse<Template[]>> {
    try {
      let url = '/api/templates';
      if (storeId) {
        url += `?storeId=${encodeURIComponent(storeId)}`;
      }

      const response = await this.axiosInstance.get(url);

      // 處理不同的回應格式
      let templates: Template[] = [];
      if (Array.isArray(response.data)) {
        templates = response.data;
      } else if (response.data.templates && Array.isArray(response.data.templates)) {
        templates = response.data.templates;
      } else if (response.data.data && Array.isArray(response.data.data)) {
        templates = response.data.data;
      }

      return {
        success: true,
        data: templates,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 獲取單個模板詳情
   */
  async getTemplate(templateId: string, storeId?: string): Promise<ApiResponse<Template>> {
    try {
      let url = `/api/templates/${templateId}`;
      if (storeId) {
        url += `?storeId=${storeId}`;
      }

      const response = await this.axiosInstance.get(url);
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 創建新模板
   */
  async createTemplate(templateData: Partial<Template>): Promise<ApiResponse<Template>> {
    try {
      const response = await this.axiosInstance.post('/api/templates', templateData);
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 更新模板
   */
  async updateTemplate(templateId: string, updates: Partial<Template>): Promise<ApiResponse<Template>> {
    try {
      const response = await this.axiosInstance.put(`/api/templates/${templateId}`, updates);
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 刪除模板
   */
  async deleteTemplate(templateId: string): Promise<ApiResponse> {
    try {
      await this.axiosInstance.delete(`/api/templates/${templateId}`);
      return {
        success: true,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // ==================== 工具方法 ====================

  /**
   * 測試服務器連接
   */
  async testConnection(): Promise<ApiResponse> {
    try {
      const response = await this.axiosInstance.get('/api/health');
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 獲取當前基礎 URL
   */
  getBaseURL(): string {
    return this.baseURL;
  }
}

// 創建單例實例
export const apiService = new ApiService();
export default apiService;
