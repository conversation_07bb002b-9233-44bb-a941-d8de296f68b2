# EPD Manager App - WebSocket IP 地址動態配置更新

## 📋 更新概述

修正了 WebSocket 服務中固定 IP 地址的問題，現在 WebSocket 連接會自動使用用戶在登入頁面設定的服務器 IP 地址。

## 🎯 更新目標

- **動態 IP 配置**：WebSocket 連接使用用戶設定的服務器 IP，而不是固定的 *************
- **統一配置管理**：WebSocket 和 API 服務使用相同的服務器地址
- **自動同步**：用戶更改服務器 IP 時，WebSocket 連接會自動使用新地址

## 🔧 修改的文件

### 1. `src/config/websocket.ts`
**主要修改**：
- 新增 `getUserServerIp()` 函數，從 AsyncStorage 獲取用戶設定的 IP
- 修改 `getFrontendWebSocketUrl()` 為異步函數，動態獲取 IP
- 移除固定的開發/生產環境配置，改用統一的默認配置

**新增功能**：
```typescript
// 動態獲取用戶設定的服務器 IP
export const getUserServerIp = async (): Promise<string> => {
  const savedIp = await AsyncStorage.getItem(STORAGE_KEYS.SERVER_IP);
  return savedIp || WEBSOCKET_CONFIG.DEFAULT.HOST;
};

// 異步獲取 WebSocket URL
export const getFrontendWebSocketUrl = async (): Promise<string> => {
  const serverIp = await getUserServerIp();
  return `ws://${serverIp}:3001/ws`;
};
```

### 2. `src/services/WebSocketService.ts`
**主要修改**：
- 修改 `FrontendWebSocketClient` 構造函數，移除固定 URL 參數
- 更新 `connect()` 方法，在連接時動態獲取 WebSocket URL
- 導入新的配置函數

**修改前**：
```typescript
constructor(private serverUrl: string) {}

// 使用固定 URL 連接
this.ws = new WebSocket(this.serverUrl);
```

**修改後**：
```typescript
constructor() {}

// 動態獲取 URL 後連接
this.serverUrl = await getFrontendWebSocketUrl();
this.ws = new WebSocket(this.serverUrl);
```

## 🚀 工作流程

### 用戶設定服務器 IP
1. 用戶在登入頁面設定服務器 IP（例如：*************）
2. IP 地址保存到 AsyncStorage（鍵名：`@epd_manager_server_ip`）
3. API 服務使用此 IP 進行 HTTP 請求

### WebSocket 連接
1. 設備管理頁面加載時，調用 `connectWebSocket()`
2. `FrontendWebSocketClient.connect()` 動態獲取用戶設定的 IP
3. 構建 WebSocket URL：`ws://[用戶設定的IP]:3001/ws`
4. 建立 WebSocket 連接並訂閱設備狀態更新

### 自動同步
- 用戶更改服務器 IP 後，下次 WebSocket 連接會自動使用新 IP
- 無需重啟應用或手動修改代碼

## ✅ 測試驗證

### 測試案例
1. **默認 IP 測試**：
   - 首次安裝應用，未設定服務器 IP
   - WebSocket 應使用默認 IP（localhost）

2. **用戶設定 IP 測試**：
   - 在登入頁面設定服務器 IP（例如：*************）
   - WebSocket 應使用用戶設定的 IP

3. **IP 更改測試**：
   - 更改服務器 IP 設定
   - 重新進入設備管理頁面
   - WebSocket 應使用新的 IP 地址

### 驗證方法
檢查控制台日誌，確認 WebSocket 連接使用正確的 IP：
```
嘗試連接到前端 WebSocket 服務: ws://*************:3001/ws
```

## 📱 用戶體驗改善

### 之前的問題
- WebSocket IP 固定為 *************
- 用戶設定不同的服務器 IP 時，WebSocket 連接失敗
- 需要手動修改代碼才能更改 WebSocket IP

### 現在的體驗
- WebSocket 自動使用用戶設定的服務器 IP
- 統一的服務器配置管理
- 無需手動修改代碼
- 即時更新功能正常工作

## 🎉 更新完成

現在 EPD Manager App 的 WebSocket 連接會自動使用用戶設定的服務器 IP 地址，提供一致的配置體驗。用戶只需要在登入頁面設定一次服務器 IP，所有網絡連接（API 和 WebSocket）都會使用相同的地址。
