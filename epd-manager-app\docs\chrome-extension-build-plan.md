# EPD Manager App Chrome Extension 開發規劃

## 一、概述

本文檔提供將現有的 EPD Manager App 擴展為 Chrome Extension 的完整規劃。Chrome Extension 將提供類似於移動應用的功能，但針對桌面瀏覽器環境進行優化，使用戶能夠在 Chrome 瀏覽器中直接管理電子紙顯示器系統。

## 二、目標功能

Chrome Extension 版本將實現以下核心功能：

1. **用戶認證**：與現有移動應用相同的登入認證功能
2. **門店選擇**：支持選擇管理的門店
3. **設備管理**：瀏覽和管理電子紙設備
4. **網關管理**：監控網關狀態和連接情況
5. **通訊監控**：監控 WebSocket 通信日誌
6. **本地通知**：提供瀏覽器通知功能，通知設備狀態變更

## 三、技術架構

### 3.1 目錄結構設計

在現有 epd-manager-app 目錄下添加 Chrome Extension 相關文件：

```
epd-manager-app/
├── chrome-extension/          <- 新增 Chrome Extension 相關文件
│   ├── manifest.json          <- Chrome Extension 配置文件
│   ├── background/            <- 背景腳本
│   │   └── background.ts      <- 背景服務腳本
│   ├── popup/                 <- 彈出窗口界面
│   │   ├── index.html         <- 彈出窗口 HTML
│   │   ├── popup.tsx          <- 彈出窗口主組件
│   │   └── popup.css          <- 彈出窗口樣式
│   ├── options/               <- 擴展選項頁面
│   │   ├── index.html         <- 選項頁面 HTML
│   │   └── options.tsx        <- 選項頁面組件
│   ├── content/               <- 內容腳本 (如需要)
│   │   └── content.ts         <- 注入頁面的腳本
│   └── assets/                <- 圖標和靜態資源
└── scripts/                   <- 構建腳本
    ├── build-chrome-ext.js    <- Chrome Extension 構建腳本
    └── ...
```

### 3.2 組件共享策略

為了最大化代碼複用，將採用以下策略：

1. **共享核心邏輯**：
   - 從現有 `src/services` 和 `src/utils` 中提取通用邏輯
   - 建立共享庫，供移動端和擴展端同時使用

2. **UI 組件差異化**：
   - 移動端使用 React Native 組件
   - 擴展端使用 React Web 組件，調整為適合擴展彈出窗口的尺寸和樣式

3. **狀態管理**：
   - 繼續使用 Zustand 作為狀態管理方案
   - 適配瀏覽器存儲機制 (localStorage/chrome.storage)

## 四、編譯系統設計

### 4.1 技術棧選擇

- **框架**：React + TypeScript
- **構建工具**：Vite (用於替代目前使用的 Expo)
- **打包工具**：@crxjs/vite-plugin (專用於構建 Chrome Extensions)
- **CSS 處理**：使用 CSS modules 或 TailwindCSS

### 4.2 開發環境配置

```json
// 需要添加的開發依賴
{
  "devDependencies": {
    "@crxjs/vite-plugin": "^2.0.0",
    "@types/chrome": "^0.0.254",
    "vite": "^4.5.2"
  }
}
```

### 4.3 構建腳本設計

在 `package.json` 中添加以下新腳本：

```json
{
  "scripts": {
    "dev:chrome": "vite build --watch --mode development --config vite.config.chrome.ts",
    "build:chrome:dev": "vite build --mode development --config vite.config.chrome.ts",
    "build:chrome:prod": "vite build --mode production --config vite.config.chrome.ts"
  }
}
```

### 4.4 Vite 配置設計

創建專用於 Chrome Extension 構建的 Vite 配置文件：

```typescript
// vite.config.chrome.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { crx } from '@crxjs/vite-plugin';
import manifest from './chrome-extension/manifest.json';

export default defineConfig({
  plugins: [
    react(),
    crx({ manifest }),
  ],
  build: {
    outDir: 'dist/chrome-extension',
    emptyOutDir: true,
    rollupOptions: {
      input: {
        popup: 'chrome-extension/popup/index.html',
        options: 'chrome-extension/options/index.html',
        background: 'chrome-extension/background/background.ts',
        content: 'chrome-extension/content/content.ts',
      },
    },
  },
});
```

## 五、關鍵文件設計

### 5.1 manifest.json

```json
{
  "manifest_version": 3,
  "name": "EPD Manager",
  "version": "1.0.0",
  "description": "管理電子紙顯示器系統的 Chrome 擴展",
  "action": {
    "default_popup": "popup/index.html",
    "default_icon": {
      "16": "assets/icon16.png",
      "48": "assets/icon48.png",
      "128": "assets/icon128.png"
    }
  },
  "options_page": "options/index.html",
  "background": {
    "service_worker": "background/background.js",
    "type": "module"
  },
  "permissions": [
    "storage",
    "notifications",
    "webRequest"
  ],
  "host_permissions": [
    "*://api.example.com/*"
  ]
}
```

### 5.2 背景腳本設計

背景腳本將處理以下功能：

1. WebSocket 連接維護
2. 設備狀態監控
3. 本地通知發送
4. 後台數據同步

### 5.3 彈出窗口設計

彈出窗口將提供簡潔的界面，包含以下功能：

1. 登入/登出按鈕
2. 門店選擇下拉菜單
3. 網關連接狀態指示器
4. 設備狀態概覽
5. 快速操作按鈕（刷新、重連等）

## 六、適配挑戰與解決方案

### 6.1 API 適配層

由於 Chrome Extension 和移動應用的 API 調用環境不同，需要建立適配層：

1. **存儲適配**：
   - 移動端：AsyncStorage
   - 擴展端：chrome.storage.local

2. **通知適配**：
   - 移動端：本地推送通知
   - 擴展端：chrome.notifications API

### 6.2 UI 響應式設計

擴展彈出窗口尺寸受限，需要重新設計 UI：

1. **精簡佈局**：簡化界面，保留核心功能
2. **折疊面板**：使用手風琴組件節省空間
3. **選項頁面**：複雜設置放入選項頁面

### 6.3 WebSocket 連接處理

Chrome Extension 中 WebSocket 連接的處理：

1. **背景保活**：在背景腳本中維持 WebSocket 連接
2. **連接共享**：使用 chrome.runtime.connect 在彈出窗口和背景腳本間共享連接
3. **重連策略**：實現指數退避重連機制

## 七、開發路線圖

### 7.1 第一階段：基礎架構

1. 設置 Chrome Extension 專用構建流程
2. 創建基本的 manifest.json 和目錄結構
3. 實現簡單的彈出窗口 UI
4. 建立存儲適配層

### 7.2 第二階段：核心功能

1. 實現用戶認證和登入功能
2. 添加門店選擇功能
3. 移植 WebSocket 服務到背景腳本
4. 創建基本設備狀態監控界面

### 7.3 第三階段：擴展功能

1. 完善設備管理界面
2. 添加通知功能
3. 實現選項頁面配置
4. 優化性能和用戶體驗

### 7.4 第四階段：測試和優化

1. 進行跨瀏覽器兼容性測試
2. 優化資源使用和加載效能
3. 用戶體驗改進和 UI 精細調整

## 八、測試策略

### 8.1 單元測試

使用 Jest 和 React Testing Library 進行組件和服務的單元測試。

### 8.2 集成測試

模擬 Chrome Extension API，測試不同部分之間的交互。

### 8.3 手動測試

在 Chrome 開發者模式中加載未打包擴展進行功能測試。

### 8.4 自動化 E2E 測試

使用 Playwright 或 Puppeteer 進行自動化端對端測試。

## 九、發布流程

1. 構建生產版本擴展
2. 打包 ZIP 文件
3. 提交到 Chrome Web Store 進行審核
4. 處理審核反饋並更新
5. 發布到 Chrome Web Store

## 十、結論

通過添加 Chrome Extension 版本，EPD Manager App 將能夠在更多平台提供服務，使用戶能夠在桌面環境中方便地管理電子紙顯示系統。本規劃提供了完整的開發路線圖，從架構設計到功能實現，為後續開發工作提供明確指導。
