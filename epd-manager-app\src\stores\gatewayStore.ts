// EPD Manager App - 網關狀態管理

import { create } from 'zustand';
import { Gateway, GatewayState, ConnectionStatus } from '../types';
import { apiService } from '../services/ApiService';
import { autoPairingService } from '../services/AutoPairingService';
import { webSocketService, frontendWebSocketClient, subscribeToGatewayStatus } from '../services/WebSocketService';
import { CONNECTION_STATUS } from '../utils/constants';

interface GatewayStore extends GatewayState {
  // Actions
  fetchGateways: (storeId: string) => Promise<boolean>;
  selectGateway: (gateway: Gateway) => void;
  clearSelectedGateway: () => void;
  autoPairGateway: (storeId: string, gatewayName?: string, macAddress?: string) => Promise<boolean>;
  connectToGateway: (gateway: Gateway, storeId: string) => Promise<boolean>;
  disconnectGateway: () => void;
  updateConnectionStatus: (status: ConnectionStatus) => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;

  // WebSocket 相關
  connectFrontendWebSocket: () => Promise<boolean>;
  disconnectFrontendWebSocket: () => void;
  subscribeToRealTimeUpdates: (storeId: string) => () => void;
  isFrontendWebSocketConnected: () => boolean;

  // Getters
  getGatewayById: (gatewayId: string) => Gateway | undefined;
  hasSelectedGateway: () => boolean;
  isConnected: () => boolean;
  getCurrentGateway: () => Gateway | null;
}

export const useGatewayStore = create<GatewayStore>()((set, get) => ({
  // Initial state
  gateways: [],
  selectedGateway: null,
  connectionStatus: CONNECTION_STATUS.DISCONNECTED,
  loading: false,
  error: null,

  // Actions
  fetchGateways: async (storeId: string) => {
    set({ loading: true, error: null });

    try {
      const result = await apiService.getGateways(storeId);

      if (result.success && result.data) {
        set({
          gateways: result.data,
          loading: false,
          error: null
        });
        return true;
      } else {
        set({
          loading: false,
          error: result.error || '獲取網關列表失敗'
        });
        return false;
      }
    } catch (error: any) {
      set({
        loading: false,
        error: error.message || '獲取網關列表失敗'
      });
      return false;
    }
  },

  selectGateway: (gateway: Gateway) => {
    set({ selectedGateway: gateway });
    console.log('選擇網關:', gateway.name);
  },

  clearSelectedGateway: () => {
    set({ selectedGateway: null });
    console.log('清除選擇的網關');
  },

  autoPairGateway: async (storeId: string, gatewayName?: string, macAddress?: string) => {
    set({ loading: true, error: null });

    try {
      const result = await autoPairingService.autoCreateAndConnectGateway(storeId, gatewayName, macAddress);

      if (result.success && result.gateway) {
        // 更新網關列表
        const { gateways } = get();
        const updatedGateways = [...gateways, result.gateway];
        
        set({
          gateways: updatedGateways,
          selectedGateway: result.gateway,
          connectionStatus: CONNECTION_STATUS.CONNECTED,
          loading: false,
          error: null
        });

        console.log('自動配對成功:', result.gateway.name);
        return true;
      } else {
        set({
          loading: false,
          error: result.error || '自動配對失敗'
        });
        return false;
      }
    } catch (error: any) {
      set({
        loading: false,
        error: error.message || '自動配對失敗'
      });
      return false;
    }
  },

  connectToGateway: async (gateway: Gateway, storeId: string) => {
    set({ loading: true, error: null, connectionStatus: CONNECTION_STATUS.CONNECTING });

    try {
      const result = await autoPairingService.reconnectToGateway(gateway, storeId);

      if (result.success && result.gateway) {
        set({
          selectedGateway: result.gateway,
          connectionStatus: CONNECTION_STATUS.CONNECTED,
          loading: false,
          error: null
        });

        console.log('連接網關成功:', result.gateway.name);
        return true;
      } else {
        set({
          connectionStatus: CONNECTION_STATUS.ERROR,
          loading: false,
          error: result.error || '連接網關失敗'
        });
        return false;
      }
    } catch (error: any) {
      set({
        connectionStatus: CONNECTION_STATUS.ERROR,
        loading: false,
        error: error.message || '連接網關失敗'
      });
      return false;
    }
  },

  disconnectGateway: () => {
    try {
      autoPairingService.disconnectCurrentGateway();
      set({
        connectionStatus: CONNECTION_STATUS.DISCONNECTED,
        selectedGateway: null
      });
      console.log('網關已斷開連接');
    } catch (error: any) {
      console.error('斷開網關連接失敗:', error);
      set({ error: error.message });
    }
  },

  updateConnectionStatus: (status: ConnectionStatus) => {
    set({ connectionStatus: status });
  },

  clearError: () => {
    set({ error: null });
  },

  setLoading: (loading: boolean) => {
    set({ loading });
  },

  setError: (error: string | null) => {
    set({ error });
  },

  // WebSocket 相關方法
  connectFrontendWebSocket: async () => {
    try {
      const connected = await frontendWebSocketClient.connect();
      if (connected) {
        console.log('前端 WebSocket 連接成功（網關管理）');
        return true;
      } else {
        console.error('前端 WebSocket 連接失敗（網關管理）');
        set({ error: '無法連接到即時更新服務' });
        return false;
      }
    } catch (error: any) {
      console.error('連接前端 WebSocket 失敗（網關管理）:', error);
      set({ error: error.message || '連接即時更新服務失敗' });
      return false;
    }
  },

  disconnectFrontendWebSocket: () => {
    frontendWebSocketClient.disconnect();
    console.log('前端 WebSocket 已斷開（網關管理）');
  },

  subscribeToRealTimeUpdates: (storeId: string) => {
    console.log(`訂閱門店 ${storeId} 的即時網關更新`);

    const handleGatewayUpdate = (event: any) => {
      console.log('收到網關狀態更新事件:', event);

      if (event.gateways && Array.isArray(event.gateways)) {
        // 更新網關列表
        set(state => {
          const updatedGateways = [...state.gateways];

          event.gateways.forEach((updatedGateway: Gateway) => {
            const index = updatedGateways.findIndex(g => g._id === updatedGateway._id);
            if (index >= 0) {
              // 更新現有網關
              updatedGateways[index] = {
                ...updatedGateways[index],
                ...updatedGateway,
                lastSeen: updatedGateway.lastSeen ? new Date(updatedGateway.lastSeen) : null,
                updatedAt: updatedGateway.updatedAt ? new Date(updatedGateway.updatedAt) : null,
              };
            } else if (event.updateType === 'create') {
              // 添加新網關
              updatedGateways.push({
                ...updatedGateway,
                lastSeen: updatedGateway.lastSeen ? new Date(updatedGateway.lastSeen) : null,
                createdAt: updatedGateway.createdAt ? new Date(updatedGateway.createdAt) : null,
                updatedAt: updatedGateway.updatedAt ? new Date(updatedGateway.updatedAt) : null,
              });
            }
          });

          return { gateways: updatedGateways };
        });

        console.log(`已更新 ${event.gateways.length} 個網關的狀態`);
      }
    };

    return subscribeToGatewayStatus(storeId, handleGatewayUpdate, {
      includeConnectionInfo: true,
      includeFirmwareInfo: true
    });
  },

  isFrontendWebSocketConnected: () => {
    return frontendWebSocketClient.getConnectionStatus();
  },

  // Getters
  getGatewayById: (gatewayId: string) => {
    const { gateways } = get();
    return gateways.find(gateway => gateway._id === gatewayId);
  },

  hasSelectedGateway: () => {
    const { selectedGateway } = get();
    return !!selectedGateway;
  },

  isConnected: () => {
    const { connectionStatus } = get();
    return connectionStatus === CONNECTION_STATUS.CONNECTED;
  },

  getCurrentGateway: () => {
    const { selectedGateway } = get();
    return selectedGateway;
  }
}));

// 設置 WebSocket 狀態變化監聽器
webSocketService.addStatusChangeHandler((status: ConnectionStatus) => {
  useGatewayStore.getState().updateConnectionStatus(status);
});

// 導出便捷的 hooks
export const useGateways = () => {
  const store = useGatewayStore();
  return {
    // State
    gateways: store.gateways,
    selectedGateway: store.selectedGateway,
    connectionStatus: store.connectionStatus,
    loading: store.loading,
    error: store.error,

    // Actions
    fetchGateways: store.fetchGateways,
    selectGateway: store.selectGateway,
    clearSelectedGateway: store.clearSelectedGateway,
    autoPairGateway: store.autoPairGateway,
    connectToGateway: store.connectToGateway,
    disconnectGateway: store.disconnectGateway,
    clearError: store.clearError,

    // WebSocket 相關
    connectFrontendWebSocket: store.connectFrontendWebSocket,
    disconnectFrontendWebSocket: store.disconnectFrontendWebSocket,
    subscribeToRealTimeUpdates: store.subscribeToRealTimeUpdates,
    isFrontendWebSocketConnected: store.isFrontendWebSocketConnected,

    // Getters
    getGatewayById: store.getGatewayById,
    hasSelectedGateway: store.hasSelectedGateway(),
    isConnected: store.isConnected(),
    getCurrentGateway: store.getCurrentGateway,
  };
};

export const useGatewayActions = () => {
  const store = useGatewayStore();
  return {
    fetchGateways: store.fetchGateways,
    selectGateway: store.selectGateway,
    clearSelectedGateway: store.clearSelectedGateway,
    autoPairGateway: store.autoPairGateway,
    connectToGateway: store.connectToGateway,
    disconnectGateway: store.disconnectGateway,
    updateConnectionStatus: store.updateConnectionStatus,
    clearError: store.clearError,
    setLoading: store.setLoading,
    setError: store.setError,
  };
};

export const useGatewayState = () => {
  const store = useGatewayStore();
  return {
    gateways: store.gateways,
    selectedGateway: store.selectedGateway,
    connectionStatus: store.connectionStatus,
    loading: store.loading,
    error: store.error,
    hasSelectedGateway: store.hasSelectedGateway(),
    isConnected: store.isConnected(),
    getCurrentGateway: store.getCurrentGateway,
  };
};
