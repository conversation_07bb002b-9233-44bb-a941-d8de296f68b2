# EPD Manager 部署包

這是 EPD Manager 的完整部署包，包含所有必要的 Docker 鏡像和配置檔案，可以在任何支援 Docker 的 Linux 或 Windows 主機上快速部署。

## 📦 包含內容

- `epd-manager-all.tar` - 完整鏡像包 (EPD Manager 應用 + MongoDB 資料庫)
- `docker-compose.yml` - Docker Compose 配置檔案
- `.env.example` - 環境變數範例檔案
- `export-images.bat` - Windows 鏡像導出腳本
- `export-images.sh` - Linux/macOS 鏡像導出腳本
- `deploy.bat` - Windows 部署腳本
- `deploy.sh` - Linux/macOS 部署腳本
- `README.md` - 本說明檔案

## 🚀 快速部署

### Windows 部署

1. 確保已安裝 [Docker Desktop](https://www.docker.com/products/docker-desktop)
2. 雙擊執行 `deploy.bat`
3. 按照提示完成部署

### Linux/macOS 部署

1. 確保已安裝 Docker 和 docker-compose
2. 執行部署腳本：
   ```bash
   ./deploy.sh
   ```

## ⚙️ 手動部署步驟

如果自動腳本無法使用，可以按照以下步驟手動部署：

### 1. 準備環境檔案

```bash
# 複製環境變數範例檔案
cp .env.example .env

# 編輯環境變數（重要：請修改 JWT_SECRET）
nano .env  # Linux
notepad .env  # Windows
```

### 2. 載入 Docker 鏡像

```bash
# 載入所有鏡像 (EPD Manager + MongoDB)
docker load -i epd-manager-all.tar
```

### 3. 啟動服務

```bash
# 啟動所有服務
docker-compose up -d

# 查看服務狀態
docker-compose ps

# 查看日誌
docker-compose logs -f
```

## 🔧 配置說明

### 環境變數配置 (.env)

| 變數名 | 預設值 | 說明 |
|--------|--------|------|
| `JWT_SECRET` | - | **必須設置** JWT 密鑰，請設置為隨機字串 |
| `FRONTEND_PORT` | 5173 | 前端服務外部端口 |
| `SERVER_PORT` | 3001 | 後端服務外部端口 |
| `MONGO_PORT` | 27017 | MongoDB 外部端口 |
| `NODE_ENV` | production | 運行環境 |
| `MONGO_DB` | resourceManagement | MongoDB 資料庫名稱 |

### 重要安全提醒

1. **必須修改 JWT_SECRET**：請設置為強隨機字串
2. **設置管理員帳號**：首次訪問時設置管理員帳號和密碼
3. **端口安全**：生產環境建議修改預設端口
4. **防火牆設置**：確保只開放必要的端口

## 🌐 訪問服務

部署完成後，可以通過以下地址訪問：

- **前端界面**: http://localhost:5173
- **後端API**: http://localhost:3001
- **MongoDB**: localhost:27017

### 首次訪問設置

⚠️ **重要**：首次部署後需要進行系統初始化

1. 開啟瀏覽器訪問 http://localhost:5173
2. 系統會自動顯示初始化頁面
3. 設置管理員帳號和密碼
4. 完成初始化後即可正常使用

💡 **提示**：請設置強密碼以確保安全！

## 🛠️ 常用管理命令

```bash
# 查看服務狀態
docker-compose ps

# 查看日誌
docker-compose logs -f

# 重啟服務
docker-compose restart

# 停止服務
docker-compose down

# 更新並重啟
docker-compose down && docker-compose up -d

# 清理未使用的鏡像
docker image prune
```

## 🔍 故障排除

### 常見問題

1. **端口被佔用**
   - 修改 `.env` 檔案中的端口配置
   - 或停止佔用端口的其他服務

2. **Docker 未運行**
   - Windows: 啟動 Docker Desktop
   - Linux: `sudo systemctl start docker`

3. **權限問題** (Linux)
   - 確保當前用戶在 docker 群組中：`sudo usermod -aG docker $USER`
   - 重新登入或執行：`newgrp docker`

4. **記憶體不足**
   - MongoDB 預設使用 1.5GB 記憶體快取
   - 可在 docker-compose.yml 中調整 `--wiredTigerCacheSizeGB` 參數

### 查看詳細日誌

```bash
# 查看特定服務日誌
docker-compose logs epd-manager
docker-compose logs mongodb

# 即時查看日誌
docker-compose logs -f --tail=100
```

## 📋 系統需求

### 最低需求
- **CPU**: 2 核心
- **記憶體**: 4GB RAM
- **硬碟**: 10GB 可用空間
- **Docker**: 20.10+ 版本
- **docker-compose**: 1.29+ 版本

### 建議需求
- **CPU**: 4 核心
- **記憶體**: 8GB RAM
- **硬碟**: 20GB 可用空間（含資料存儲）

## 📞 技術支援

如遇到問題，請檢查：

1. Docker 和 docker-compose 版本是否符合需求
2. 系統資源是否充足
3. 網路連接是否正常
4. 防火牆設置是否正確

更多技術文件請參考專案原始碼中的 `docs` 目錄。


## 結構
release/
├── 📄 .env.example          # 環境變數範例檔案
├── 📄 docker-compose.yml    # 生產環境 Docker Compose 配置
├── 📄 README.md             # 詳細部署說明文件
├── 📄 QUICK_START.md        # 快速開始指南
├── 📄 VERSION               # 版本資訊
├── 🔧 export-images.bat     # Windows 鏡像導出腳本
├── 🔧 export-images.sh      # Linux/macOS 鏡像導出腳本
├── 🚀 deploy.bat            # Windows 部署腳本
├── 🚀 deploy.sh             # Linux/macOS 部署腳本
├── 🛑 stop.bat              # Windows 停止腳本
├── 🛑 stop.sh               # Linux/macOS 停止腳本
├── 📊 check-status.bat      # Windows 狀態檢查腳本
└── 📊 check-status.sh       # Linux/macOS 狀態檢查腳本