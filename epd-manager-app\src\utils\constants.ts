// EPD Manager App - 常量定義

// API 配置
export const API_CONFIG = {
  DEFAULT_HOST: 'localhost',
  DEFAULT_PORT: 3001,
  DEFAULT_WEB_PORT: 5173,  
  DEFAULT_PROTOCOL: 'http',
  TIMEOUT: 10000, // 10 秒
  RETRY_ATTEMPTS: 3,
} as const;

// WebSocket 配置
export const WEBSOCKET_CONFIG = {
  PING_INTERVAL: 25000,        // 25 秒心跳間隔
  DEVICE_STATUS_INTERVAL: 5000, // 5 秒設備狀態間隔
  GATEWAY_INFO_INTERVAL: 30000, // 30 秒網關信息間隔
  RECONNECT_INTERVAL: 5000,     // 5 秒重連間隔
  MAX_RECONNECT_ATTEMPTS: 5,    // 最大重連次數
} as const;

// 存儲鍵名
export const STORAGE_KEYS = {
  AUTH_TOKEN: '@epd_manager_auth_token',
  USER_INFO: '@epd_manager_user_info',
  SELECTED_STORE: '@epd_manager_selected_store',
  APP_SETTINGS: '@epd_manager_app_settings',
  GATEWAY_LIST: '@epd_manager_gateway_list',
  DEVICE_IMAGE_CODES: '@epd_manager_device_image_codes',
  REMEMBER_LOGIN: '@epd_manager_remember_login',
  SERVER_IP: '@epd_manager_server_ip',
} as const;

// 設備相關常量
export const DEVICE_CONSTANTS = {
  SIZES: ['1.54"', '2.13"', '2.9"', '3.7"', '4.2"', '5.83"', '6"', '7.5"', '10.3"'],
  COLOR_TYPES: ['BW', 'BWR', 'BWRY'] as const,
  STATUS_TYPES: ['online', 'offline'] as const,
  DEFAULT_SIZE: '2.9"',
  DEFAULT_COLOR_TYPE: 'BW' as const,
  DEFAULT_STATUS: 'online' as const,
} as const;

// 網關相關常量
export const GATEWAY_CONSTANTS = {
  MODELS: ['GW-1000', 'GW-2000', 'GW-3000', 'EPD-Gateway-Pro'],
  STATUS_TYPES: ['online', 'offline'] as const,
  DEFAULT_MODEL: 'GW-2000',
  DEFAULT_STATUS: 'online' as const,
  DEFAULT_WIFI_VERSION: '1.0.0',
  DEFAULT_BT_VERSION: '2.0.0',
} as const;

// WebSocket 消息類型
export const WS_MESSAGE_TYPES = {
  // 發送的消息類型
  PING: 'ping',
  DEVICE_STATUS: 'deviceStatus',
  GATEWAY_INFO: 'gatewayInfo',
  REQUEST_PREVIEW_IMAGE: 'requestPreviewImage',
  
  // 接收的消息類型
  WELCOME: 'welcome',
  PONG: 'pong',
  GATEWAY_INFO_ACK: 'gatewayInfoAck',
  UPDATE_PREVIEW: 'update_preview',
  ERROR: 'error',
} as const;

// 連接狀態
export const CONNECTION_STATUS = {
  DISCONNECTED: 'disconnected',
  CONNECTING: 'connecting',
  CONNECTED: 'connected',
  ERROR: 'error',
} as const;

// 日誌級別
export const LOG_LEVELS = {
  DEBUG: 'debug',
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error',
} as const;

// 顏色定義
export const COLORS = {
  PRIMARY: '#1976D2',
  SECONDARY: '#424242',
  SUCCESS: '#4CAF50',
  WARNING: '#FF9800',
  ERROR: '#F44336',
  INFO: '#2196F3',
  
  // 連接狀態顏色
  CONNECTED: '#4CAF50',
  CONNECTING: '#FF9800',
  DISCONNECTED: '#9E9E9E',
  CONNECTION_ERROR: '#F44336',
  
  // 設備狀態顏色
  DEVICE_ONLINE: '#4CAF50',
  DEVICE_OFFLINE: '#9E9E9E',
  
  // 背景顏色
  BACKGROUND: '#FAFAFA',
  SURFACE: '#FFFFFF',
  CARD_BACKGROUND: '#FFFFFF',

  // 邊框顏色
  BORDER: '#E0E0E0',

  // 文字顏色
  TEXT_PRIMARY: '#212121',
  TEXT_SECONDARY: '#757575',
  TEXT_DISABLED: '#BDBDBD',
  WHITE: '#FFFFFF',
} as const;

// 尺寸定義
export const SIZES = {
  // 間距
  SPACING_XS: 4,
  SPACING_SM: 8,
  SPACING_MD: 16,
  SPACING_LG: 24,
  SPACING_XL: 32,
  
  // 圓角
  BORDER_RADIUS_SM: 4,
  BORDER_RADIUS_MD: 8,
  BORDER_RADIUS_LG: 12,
  
  // 字體大小
  FONT_SIZE_XS: 12,
  FONT_SIZE_SM: 14,
  FONT_SIZE_MD: 16,
  FONT_SIZE_LG: 18,
  FONT_SIZE_XL: 20,
  FONT_SIZE_XXL: 24,
  
  // 圖標大小
  ICON_SIZE_SM: 16,
  ICON_SIZE_MD: 24,
  ICON_SIZE_LG: 32,
  ICON_SIZE_XL: 48,
} as const;

// 動畫持續時間
export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
} as const;

// 錯誤消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '網絡連接錯誤，請檢查網絡設置',
  AUTH_FAILED: '認證失敗，請檢查用戶名和密碼',
  TOKEN_EXPIRED: '登入已過期，請重新登入',
  SERVER_ERROR: '服務器錯誤，請稍後再試',
  INVALID_MAC_ADDRESS: 'MAC 地址格式不正確，請使用格式 AA:BB:CC:DD:EE:FF',
  INVALID_IP_ADDRESS: 'IP 地址格式不正確',
  WEBSOCKET_CONNECTION_FAILED: 'WebSocket 連接失敗',
  GATEWAY_CREATION_FAILED: '網關創建失敗',
  DEVICE_ADD_FAILED: '設備添加失敗',
  AUTO_PAIRING_FAILED: '自動配對失敗',
} as const;

// 成功消息
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: '登入成功',
  LOGOUT_SUCCESS: '登出成功',
  GATEWAY_CREATED: '網關創建成功',
  GATEWAY_CONNECTED: '網關連接成功',
  GATEWAY_DISCONNECTED: '網關已斷開連接',
  DEVICE_ADDED: '設備添加成功',
  DEVICE_REMOVED: '設備移除成功',
  AUTO_PAIRING_SUCCESS: '自動配對成功',
  IMAGE_REQUEST_SENT: '圖像請求已發送',
} as const;

// 預設配置
export const DEFAULT_SETTINGS = {
  serverUrl: `${API_CONFIG.DEFAULT_PROTOCOL}://${API_CONFIG.DEFAULT_HOST}:${API_CONFIG.DEFAULT_PORT}`,
  autoReconnect: true,
  logLevel: LOG_LEVELS.INFO,
  rememberLogin: false,
} as const;

// 正則表達式
export const REGEX_PATTERNS = {
  MAC_ADDRESS: /^([0-9A-Fa-f]{2}[:]){5}([0-9A-Fa-f]{2})$/,
  IP_ADDRESS: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
  URL: /^https?:\/\/.+/,
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
} as const;

// 應用信息
export const APP_INFO = {
  NAME: 'EPD Manager',
  VERSION: '1.0.0',
  DESCRIPTION: 'EPD Manager App - 自動化橋樑應用',
  AUTHOR: 'EPD Manager Team',
} as const;
