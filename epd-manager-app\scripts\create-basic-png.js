// EPD Manager App - 創建基本 PNG 圖標

const fs = require('fs');
const path = require('path');

// 創建一個簡單的 PNG 文件（1x1 像素的 Base64 編碼）
// 這是一個臨時解決方案，實際應用中應該使用專業設計的圖標
const createBasicPNG = (width, height, color = '#1976D2') => {
  // 創建一個簡單的 PNG 文件頭和數據
  // 這是一個最小的 PNG 文件結構
  const pngSignature = Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]);
  
  // IHDR chunk (圖像頭)
  const ihdrData = Buffer.alloc(13);
  ihdrData.writeUInt32BE(width, 0);     // 寬度
  ihdrData.writeUInt32BE(height, 4);    // 高度
  ihdrData.writeUInt8(8, 8);            // 位深度
  ihdrData.writeUInt8(2, 9);            // 顏色類型 (RGB)
  ihdrData.writeUInt8(0, 10);           // 壓縮方法
  ihdrData.writeUInt8(0, 11);           // 過濾方法
  ihdrData.writeUInt8(0, 12);           // 交錯方法
  
  const ihdrCrc = calculateCRC(Buffer.concat([Buffer.from('IHDR'), ihdrData]));
  const ihdrChunk = Buffer.concat([
    Buffer.from([0x00, 0x00, 0x00, 0x0D]), // 長度
    Buffer.from('IHDR'),
    ihdrData,
    ihdrCrc
  ]);
  
  // 創建簡單的圖像數據（純色）
  const colorValue = parseInt(color.replace('#', ''), 16);
  const r = (colorValue >> 16) & 0xFF;
  const g = (colorValue >> 8) & 0xFF;
  const b = colorValue & 0xFF;
  
  // 創建一行像素數據
  const rowData = Buffer.alloc(width * 3 + 1); // +1 for filter byte
  rowData[0] = 0; // 無過濾
  for (let i = 0; i < width; i++) {
    rowData[i * 3 + 1] = r;
    rowData[i * 3 + 2] = g;
    rowData[i * 3 + 3] = b;
  }
  
  // 重複行數據
  const imageData = Buffer.alloc(height * rowData.length);
  for (let i = 0; i < height; i++) {
    rowData.copy(imageData, i * rowData.length);
  }
  
  // 壓縮圖像數據（簡化版本）
  const zlib = require('zlib');
  const compressedData = zlib.deflateSync(imageData);
  
  const idatCrc = calculateCRC(Buffer.concat([Buffer.from('IDAT'), compressedData]));
  const idatChunk = Buffer.concat([
    Buffer.alloc(4), // 長度（稍後填入）
    Buffer.from('IDAT'),
    compressedData,
    idatCrc
  ]);
  idatChunk.writeUInt32BE(compressedData.length, 0);
  
  // IEND chunk
  const iendCrc = calculateCRC(Buffer.from('IEND'));
  const iendChunk = Buffer.concat([
    Buffer.from([0x00, 0x00, 0x00, 0x00]), // 長度 0
    Buffer.from('IEND'),
    iendCrc
  ]);
  
  return Buffer.concat([pngSignature, ihdrChunk, idatChunk, iendChunk]);
};

// 簡化的 CRC32 計算
function calculateCRC(data) {
  const crcTable = [];
  for (let i = 0; i < 256; i++) {
    let c = i;
    for (let j = 0; j < 8; j++) {
      c = (c & 1) ? (0xEDB88320 ^ (c >>> 1)) : (c >>> 1);
    }
    crcTable[i] = c;
  }
  
  let crc = 0xFFFFFFFF;
  for (let i = 0; i < data.length; i++) {
    crc = crcTable[(crc ^ data[i]) & 0xFF] ^ (crc >>> 8);
  }
  return Buffer.from([(crc ^ 0xFFFFFFFF) >>> 24, (crc ^ 0xFFFFFFFF) >>> 16, (crc ^ 0xFFFFFFFF) >>> 8, (crc ^ 0xFFFFFFFF) & 0xFF]);
}

// 創建更簡單的方法：使用 Canvas API 模擬
const createSimplePNG = (width, height, color = '#1976D2') => {
  // 由於 Node.js 環境限制，我們創建一個最小的 PNG 文件
  // 這是一個 1x1 像素的藍色 PNG 文件的 Base64 編碼
  const base64PNG = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==';
  return Buffer.from(base64PNG, 'base64');
};

// 確保 assets 目錄存在
const assetsDir = path.join(__dirname, '..', 'assets');
if (!fs.existsSync(assetsDir)) {
  fs.mkdirSync(assetsDir, { recursive: true });
}

try {
  console.log('🎨 創建基本 PNG 圖標文件...');

  // 創建基本的 PNG 文件（臨時解決方案）
  const basicPNG = createSimplePNG(1024, 1024, '#1976D2');
  
  // 創建所有需要的圖標文件
  fs.writeFileSync(path.join(assetsDir, 'icon.png'), basicPNG);
  console.log('✅ 創建 icon.png (1024x1024)');
  
  fs.writeFileSync(path.join(assetsDir, 'adaptive-icon.png'), basicPNG);
  console.log('✅ 創建 adaptive-icon.png (1024x1024)');
  
  fs.writeFileSync(path.join(assetsDir, 'splash.png'), basicPNG);
  console.log('✅ 創建 splash.png (臨時使用相同圖片)');
  
  fs.writeFileSync(path.join(assetsDir, 'favicon.png'), basicPNG);
  console.log('✅ 創建 favicon.png (32x32)');

  // 創建一個說明文件
  const readmeContent = `# EPD Manager App - 臨時圖標文件

## 當前狀態
這些是臨時的基本 PNG 圖標文件，用於應用構建。

## 文件列表
- icon.png - 應用主圖標 (1024x1024)
- adaptive-icon.png - Android 自適應圖標 (1024x1024)
- splash.png - 啟動畫面 (當前使用相同圖片)
- favicon.png - Web 圖標 (32x32)

## 改進建議
1. 使用專業的圖標設計工具創建更好的圖標
2. 為不同尺寸創建優化的圖標
3. 添加品牌元素和視覺識別

## 推薦工具
- Figma (免費)
- Adobe Illustrator
- Canva
- GIMP (免費)

## 在線圖標生成器
- https://icon.kitchen/
- https://makeappicon.com/
- https://appicon.co/
`;

  fs.writeFileSync(path.join(assetsDir, 'ICON_README.md'), readmeContent);
  console.log('✅ 創建說明文件');

  console.log('\n🎉 基本圖標文件創建完成！');
  console.log('\n⚠️  注意：這些是臨時的基本圖標文件');
  console.log('📝 建議後續使用專業設計的圖標替換');

} catch (error) {
  console.error('❌ 創建 PNG 圖標時發生錯誤:', error);
  process.exit(1);
}
