import { useState, useEffect } from 'react';
import { ArrowLeft, Battery, Wifi, User, History, Router, Trash2, Send, RefreshCw, ChevronDown, ChevronRight, ChevronUp } from 'lucide-react';
import { Device } from '../types/device';
import { buildEndpointUrl } from '../utils/api/apiConfig';
import { Template } from '../types';
import { useAuthStore } from '../store/authStore';
import { DeviceEvents } from './DeviceEvents';
import { getAllGateways } from '../utils/api/gatewayApi';
import {
  setDevicePrimaryGateway,
  setDeviceGatewayMode,
  removeDeviceOtherGateway,
  sendDevicePreviewToGateway,
  updateDevice
} from '../utils/api/deviceApi';
import { regeneratePreviewBeforeSend, savePreviewImageToDevice } from '../utils/previewImageManager';
import { getAllStoreData } from '../utils/api/storeDataApi';
import { showSuccessNotification, showErrorNotification } from '../utils/bubbleNotification';

import { Gateway } from '../types/gateway';
import './FlowingBorder.css';
import { getScreenConfigs } from '../screens/screenSizeMap';
import { DisplayColorType } from '../types';
import ColorTypeGradient from './ui/ColorTypeGradient';
// 使用絕對路徑導入
import { ImageUpdateStatusBadge } from '../components/ui/ImageUpdateStatusBadge';

interface DeviceDetailPageProps {
  device: Device | null;
  onBack: () => void;
}

export default function DeviceDetailPage({ device, onBack }: DeviceDetailPageProps) {  const [templateName, setTemplateName] = useState<string>('');
  const [userDetail, setUserDetail] = useState<any>(null);
  const [activeTab, setActiveTab] = useState<'info' | 'events'>('info');
  const [allGateways, setAllGateways] = useState<Gateway[]>([]);
  const [primaryGateway, setPrimaryGateway] = useState<Gateway | null>(null);
  const [otherGateways, setOtherGateways] = useState<Gateway[]>([]);
  const [isUpdating, setIsUpdating] = useState<boolean>(false);
  const [isRefreshingStatus, setIsRefreshingStatus] = useState<boolean>(false);
  const [gatewayMode, setGatewayMode] = useState<'auto' | 'manual'>('auto');
  const [storeData, setStoreData] = useState<any[]>([]);
  const [template, setTemplate] = useState<Template | null>(null);
  const [currentDevice, setCurrentDevice] = useState<Device | null>(device);

  // 回到頂部按鈕狀態
  const [showScrollToTop, setShowScrollToTop] = useState(false);

  // 回到頂部功能
  const scrollToTop = () => {
    // 尋找實際的滾動容器
    const findScrollableContainer = () => {
      // 首先嘗試找到 App.tsx 中的滾動容器
      const appScrollContainer = document.querySelector('.flex-1.overflow-auto');
      if (appScrollContainer) {
        return appScrollContainer;
      }

      // 嘗試其他可能的滾動容器
      const containers = [
        '.overflow-auto',
        '.overflow-y-auto',
        '.overflow-scroll',
        '[style*="overflow"]'
      ];

      for (const selector of containers) {
        const container = document.querySelector(selector);
        if (container && container.scrollTop > 0) {
          return container;
        }
      }

      return null;
    };

    try {
      // 首先嘗試找到滾動容器
      const scrollContainer = findScrollableContainer();

      if (scrollContainer) {
        scrollContainer.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      } else {
        // 備用方案：window 滾動
        window.scrollTo({
          top: 0,
          left: 0,
          behavior: 'smooth'
        });

        // 如果 window 滾動也不行，直接設置
        setTimeout(() => {
          document.documentElement.scrollTop = 0;
          document.body.scrollTop = 0;
        }, 100);
      }

    } catch (error) {
      console.error('滾動到頂部失敗:', error);
      // 最後的備用方法
      document.documentElement.scrollTop = 0;
      document.body.scrollTop = 0;
    }
  };

  // 監聽滾動事件
  useEffect(() => {
    const handleScroll = (event?: Event) => {
      let scrollTop = 0;

      // 檢查是否是容器滾動事件
      if (event && event.target && event.target !== window && event.target !== document) {
        const target = event.target as Element;
        scrollTop = target.scrollTop;
      } else {
        // 檢查 window 滾動
        scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;
      }

      // 如果 window 滾動為 0，檢查是否有滾動容器
      if (scrollTop === 0) {
        const scrollContainer = document.querySelector('.flex-1.overflow-auto') ||
                              document.querySelector('.overflow-auto');
        if (scrollContainer) {
          scrollTop = scrollContainer.scrollTop;
        }
      }

      // 當滾動超過 300px 時顯示回到頂部按鈕
      const shouldShow = scrollTop > 300;
      setShowScrollToTop(shouldShow);
    };

    // 監聽 window 滾動
    window.addEventListener('scroll', handleScroll, { passive: true });
    document.addEventListener('scroll', handleScroll, { passive: true });

    // 監聽可能的滾動容器
    const scrollContainer = document.querySelector('.flex-1.overflow-auto') ||
                           document.querySelector('.overflow-auto');
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll, { passive: true });
    }

    // 初始檢查
    setTimeout(handleScroll, 100);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      document.removeEventListener('scroll', handleScroll);
      if (scrollContainer) {
        scrollContainer.removeEventListener('scroll', handleScroll);
      }
    };
  }, []);

  // 區塊收折狀態
  const [collapsedSections, setCollapsedSections] = useState<{
    details: boolean;
    preview: boolean;
    gateway: boolean;
    user: boolean;
  }>({
    details: false,
    preview: false,
    gateway: false,
    user: false
  });

  // 切換區塊收折狀態
  const toggleSection = (section: keyof typeof collapsedSections) => {
    setCollapsedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // 顯示通知 - 使用泡泡通知
  const showNotification = (message: string, type: 'success' | 'error' = 'success') => {
    if (type === 'success') {
      showSuccessNotification(message);
    } else {
      showErrorNotification(message);
    }
  };

  // 獲取模板名稱
  useEffect(() => {
    const fetchTemplateName = async () => {
      if (device?.templateId) {
        try {
          const response = await fetch(buildEndpointUrl('templates'));
          if (response.ok) {
            const templates = await response.json();
            const template = templates.find((t: Template) => t.id === device.templateId);
            if (template) {
              setTemplateName(template.name);
            } else {
              setTemplateName(device.templateId); // 如果找不到模板，顯示模板ID
            }
          }
        } catch (error) {
          console.error('獲取模板名稱失敗:', error);
          setTemplateName(device.templateId); // 出錯時顯示模板ID
        }
      } else {
        setTemplateName('');
      }
    };

    fetchTemplateName();
  }, [device?.templateId]);
  // 當設備 ID 變動時重置標籤頁並更新當前設備
  useEffect(() => {
    setActiveTab('info');
    setCurrentDevice(device);
  }, [device?._id]);
    // 獲取網關信息
  useEffect(() => {
    const fetchGatewaysInfo = async () => {
      if (!device?.storeId) return;

      try {
        // 獲取門店所有網關
        const gateways = await getAllGateways(device.storeId);
        setAllGateways(gateways);

        // 獲取主要網關信息
        if (device.primaryGatewayId) {
          const primary = gateways.find(g => g._id === device.primaryGatewayId) || null;
          setPrimaryGateway(primary);
        } else {
          setPrimaryGateway(null);
        }

        // 獲取其他網關信息
        if (device.otherGateways && device.otherGateways.length > 0) {
          const others = gateways.filter(g => device.otherGateways?.includes(g._id || ''));
          setOtherGateways(others);
        } else {
          setOtherGateways([]);
        }

        // 設置網關選擇模式
        if (device.gatewaySelectionMode) {
          setGatewayMode(device.gatewaySelectionMode);
        } else {
          setGatewayMode('manual'); // 默認為固定模式
        }
      } catch (error) {
        console.error('獲取網關信息失敗:', error);
      }
    };

    fetchGatewaysInfo();
  }, [device?.storeId, device?.primaryGatewayId, device?.otherGateways]);

  // 如果裝置已綁定使用者，則獲取該使用者資訊
  useEffect(() => {
    const fetchUserDetail = async (userId: string) => {
      try {
        const { token } = useAuthStore.getState();
        const response = await fetch(buildEndpointUrl(`users/${userId}`), {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            ...(token ? { Authorization: `Bearer ${token}` } : {}),
          },
          credentials: 'include',
        });

        if (!response.ok) {
          throw new Error('無法獲取使用者資訊');
        }

        const data = await response.json();
        setUserDetail(data);
      } catch (error) {
        console.error('獲取使用者資訊失敗:', error);
      }
    };

    if (device?.userId) {
      fetchUserDetail(device.userId);
    } else {
      setUserDetail(null);
    }
  }, [device?.userId]);

  // 獲取設備尺寸對應的 DisplayColorType
  const getDeviceColorType = (deviceSize: string | undefined): DisplayColorType | undefined => {
    if (!deviceSize) return undefined;

    // 獲取所有屏幕配置
    const screenConfigs = getScreenConfigs();

    // 標準化尺寸字符串，移除非數字和點的字符
    let normalizedSize = deviceSize.replace(/[^0-9.]/g, '');

    // 尋找匹配的屏幕配置
    for (const config of screenConfigs) {
      // 標準化配置名稱
      const configName = config.name.replace(/[^0-9.]/g, '');

      if (configName === normalizedSize) {
        // 如果找到匹配的配置，返回其支持的第一個顏色類型
        return config.supportedColors[0];
      }
    }

    // 如果找不到匹配的配置，返回默認值
    return DisplayColorType.BW;
  };

  // 當設備資料變動時，載入相關的模板和門店數據
  useEffect(() => {
    const loadTemplateAndStoreData = async () => {
      if (!device) return;

      try {
        // 載入模板數據
        if (device.templateId) {
          const response = await fetch(buildEndpointUrl(`templates/${device.templateId}`), {
            method: 'GET',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include',
          });

          if (response.ok) {
            const templateData = await response.json();
            setTemplate(templateData);
          }
        }

        // 載入門店數據
        if (device.storeId) {
          const storeDataList = await getAllStoreData(device.storeId);
          setStoreData(storeDataList);
        }

        // 如果設備沒有 colorType，則根據尺寸設置
        if (device._id && device.data && !device.data.colorType && device.data.size) {
          const colorType = getDeviceColorType(device.data.size);
          if (colorType) {
            try {
              await updateDevice(device._id, {
                data: {
                  ...device.data,
                  colorType: colorType
                }
              });
              console.log(`已根據設備尺寸設置 colorType: ${colorType}`);
            } catch (error) {
              console.error('設置 colorType 失敗:', error);
            }
          }
        }
      } catch (error) {
        console.error('載入模板和門店數據失敗:', error);
      }
    };

    loadTemplateAndStoreData();
  }, [device?._id, device?.templateId, device?.storeId, device?.data?.size]);

  // 渲染電量指示器
  const renderBatteryIndicator = (battery?: number) => {
    if (battery === undefined) {
      return <div className="flex items-center">
        <Battery className="w-5 h-5 mr-1 text-gray-400" />
        <span>未知</span>
      </div>;
    }

    let color = 'text-green-500';
    if (battery < 20) {
      color = 'text-red-500';
    } else if (battery < 50) {
      color = 'text-yellow-500';
    }

    return (
      <div className="flex items-center">
        <Battery className={`w-5 h-5 mr-1 ${color}`} />
        <span className={battery < 20 ? 'text-red-500 font-bold' : ''}>{battery}%</span>
      </div>
    );
  };
  // 渲染信號強度指示器
  const renderSignalStrength = (rssi?: number) => {
    if (rssi === undefined) {
      return <div className="flex items-center">
        <Wifi className="w-5 h-5 mr-1 text-gray-400" />
        <span>未知</span>
      </div>;
    }

    let icon = <Wifi className="w-5 h-5 mr-1 text-gray-400" />;
    let color = 'text-gray-500';

    if (rssi > -70) {
      icon = <Wifi className="w-5 h-5 mr-1 text-green-500" />;
      color = 'text-green-500';
    } else if (rssi > -85) {
      icon = <Wifi className="w-5 h-5 mr-1 text-yellow-500" />;
      color = 'text-yellow-500';
    } else {
      icon = <Wifi className="w-5 h-5 mr-1 text-red-500" />;
      color = 'text-red-500';
    }

    return (
      <div className="flex items-center">
        {icon}
        <span className={color}>{rssi} dBm</span>
      </div>
    );
  };
  // 設定主要網關
  const handleSetPrimaryGateway = async (gatewayId: string) => {
    if (!device || !device._id) return;

    try {
      setIsUpdating(true);

      // 使用 setDevicePrimaryGateway API 更新設備主要網關
      await setDevicePrimaryGateway(device._id, gatewayId, device.storeId);
        // 找到新選擇的網關
      const selectedGateway = allGateways.find(g => g._id === gatewayId) || null;

      // 記住原來的主要網關
      const oldPrimaryGateway = primaryGateway;

      // 更新主要網關
      setPrimaryGateway(selectedGateway);

      // 更新其他網關列表：
      // 1. 移除被設為主要網關的網關
      // 2. 添加原來的主要網關到其他網關列表
      let updatedOtherGateways = otherGateways.filter(g => g._id !== gatewayId);

      // 如果原本有主要網關且與新的不同，則將其添加到其他網關列表中
      if (oldPrimaryGateway && oldPrimaryGateway._id !== gatewayId) {
        updatedOtherGateways = [...updatedOtherGateways, oldPrimaryGateway];
      }
        setOtherGateways(updatedOtherGateways);
      // 顯示設置成功的通知
      showNotification(`已成功將 ${selectedGateway?.name || '未命名網關'} 設為主要網關`);

      // 自動切換為手動模式
      setGatewayMode('manual');
    } catch (error) {
      console.error('設定主要網關失敗:', error);
      showNotification('設定主要網關失敗，請稍後重試', 'error');
    } finally {
      setIsUpdating(false);
    }
  };  // 切換網關模式
  const handleToggleGatewayMode = async (mode: 'auto' | 'manual') => {
    if (!device || !device._id) return;

    try {
      setIsUpdating(true);      await setDeviceGatewayMode(device._id, mode, device.storeId);
      setGatewayMode(mode);
      showNotification(`成功切換至${mode === 'auto' ? '自動' : '手動'}模式`);
    } catch (error) {
      console.error('切換網關模式失敗:', error);
      showNotification('切換網關模式失敗', 'error');
    } finally {
      setIsUpdating(false);
    }
  };

  // 移除其他網關
  const handleRemoveOtherGateway = async (gatewayId: string) => {
    if (!device || !device._id) return;

    if (!window.confirm('確定要從其他網關列表中移除這個網關嗎？')) {
      return;
    }

    try {
      setIsUpdating(true);

      // 使用 removeDeviceOtherGateway API 更新設備其他網關列表
      await removeDeviceOtherGateway(device._id, gatewayId, device.storeId);

      // 更新其他網關列表，移除被刪除的網關
      const removedGateway = otherGateways.find(g => g._id === gatewayId);
      setOtherGateways(otherGateways.filter(g => g._id !== gatewayId));

      // 顯示移除成功的通知
      showNotification(`已成功移除網關 ${removedGateway?.name || '未命名網關'}`);
    } catch (error) {
      console.error('移除其他網關失敗:', error);
      showNotification('移除其他網關失敗，請稍後重試', 'error');
    } finally {
      setIsUpdating(false);
    }
  };
  // 刷新設備狀態
  const handleRefreshDeviceStatus = async () => {
    if (!device || !device._id) {
      showNotification('無法刷新設備狀態：設備資訊不完整', 'error');
      return;
    }

    try {
      setIsRefreshingStatus(true);

      // 從服務器獲取最新的設備狀態
      const deviceResponse = await fetch(buildEndpointUrl(`devices/${device._id}`), {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
      });

      if (deviceResponse.ok) {
        const updatedDevice = await deviceResponse.json();
        // 更新當前組件中的設備數據
        setCurrentDevice(updatedDevice);
        showNotification('設備狀態已更新');
      } else {
        showNotification('獲取設備狀態失敗', 'error');
      }
    } catch (error) {
      console.error('刷新設備狀態失敗:', error);
      showNotification('刷新設備狀態失敗', 'error');
    } finally {
      setIsRefreshingStatus(false);
    }
  };

  // 發送預覽圖到網關
  const handleSendPreviewToGateway = async (sendToAll: boolean = false) => {
    if (!device || !device._id) {
      showNotification('無法發送預覽圖：設備資訊不完整', 'error');
      return;
    }

    // 確認設備有主要網關
    if (!device.primaryGatewayId) {
      showNotification('此設備沒有主要網關，請先設置主要網關', 'error');
      return;
    }

    try {
      setIsUpdating(true);
        // 檢查是否有模板和綁定數據，如果有則重新生成預覽圖
      if (template && device.dataBindings && storeData.length > 0) {
        showNotification('正在重新生成預覽圖...', 'success');

        // 使用 regeneratePreviewBeforeSend 函數重新生成預覽圖
        const newPreviewImage = await regeneratePreviewBeforeSend(device, storeData, template);

        if (!newPreviewImage) {
          showNotification('重新生成預覽圖失敗，將使用已保存的預覽圖', 'error');
          // 如果重新生成失敗但設備沒有預覽圖，則直接返回
          if (!device.previewImage) {
            showNotification('此設備沒有預覽圖，請先綁定數據和模板', 'error');
            setIsUpdating(false);
            return;
          }
        } else {
          // 保存新生成的預覽圖到數據庫，在發送預覽圖時不需要設置imageUpdateStatus
          try {
            // 在發送預覽圖時，不需要設置imageUpdateStatus為"未更新"，也不需要更新imageCode，因為sendDevicePreviewToGateway會處理
            await savePreviewImageToDevice(device._id, newPreviewImage, device.storeId, false, false);
            console.log('已保存新生成的預覽圖到數據庫，不更新imageUpdateStatus');
            showNotification('已重新生成預覽圖並保存，正在發送...', 'success');

            // 立即更新UI顯示的預覽圖，但不更新imageUpdateStatus
            if (currentDevice) {
              setCurrentDevice({
                ...currentDevice,
                previewImage: newPreviewImage
                // 不在此處設置imageUpdateStatus，等待sendDevicePreviewToGateway後再更新
              });
            }
          } catch (saveError) {
            console.error('保存預覽圖到數據庫失敗:', saveError);
            showNotification('保存預覽圖到數據庫失敗，但將繼續發送', 'error');
          }
        }
      } else if (!device.previewImage) {
        // 如果無法重新生成且沒有預覽圖，則提示錯誤
        showNotification('此設備沒有預覽圖，請先綁定數據和模板', 'error');
        setIsUpdating(false);
        return;
      }

      // 發送預覽圖到網關
      const result = await sendDevicePreviewToGateway(device._id, {
        sendToAllGateways: sendToAll,
        storeId: device.storeId
      });

      // 如果沒有重新生成預覽圖，但需要更新UI顯示的預覽圖
      if (!currentDevice?.previewImage && device.previewImage) {
        setCurrentDevice(prev => prev ? {
          ...prev,
          previewImage: device.previewImage
          // 不在此處設置imageUpdateStatus，等待sendDevicePreviewToGateway後再更新
        } : null);
      }

      if (result.success) {
        showNotification(
          sendToAll
            ? `已成功發送預覽圖到主要網關和${result.otherGateways?.length || 0}個其它網關`
            : '已成功發送預覽圖到主要網關'
        );

        // 發送成功後立即重新獲取設備詳情，以更新圖片更新狀態
        const deviceResponse = await fetch(buildEndpointUrl(`devices/${device._id}`), {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
        });

        if (deviceResponse.ok) {
          const updatedDevice = await deviceResponse.json();
          // 更新當前組件中的設備數據
          setCurrentDevice(updatedDevice);

          // 通知父組件設備數據已更新
          window.dispatchEvent(new CustomEvent('deviceUpdated', { detail: updatedDevice }));
        }
      } else {
        showNotification(`發送預覽圖失敗：${result.error || '未知錯誤'}`, 'error');
      }
    } catch (error) {
      console.error('發送預覽圖到網關出錯:', error);
      showNotification(`發送失敗：${error instanceof Error ? error.message : '未知錯誤'}`, 'error');
    } finally {
      setIsUpdating(false);
    }
  };

  if (!device) {
    return (
      <div className="p-8 flex flex-col items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-700 mb-2">未找到設備</h2>
          <p className="text-gray-500 mb-4">找不到指定的設備資訊</p>
          <button
            onClick={onBack}
            className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            返回設備列表
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 lg:px-4 py-2 relative">
      <div className="max-w-7xl mx-auto">
        {/* 返回按鈕 */}
        <div className="mb-6">
          <button
            onClick={onBack}
            className="flex items-center text-blue-600 hover:text-blue-800"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            返回設備列表
          </button>
        </div>

        {/* 選項卡切換 */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('info')}
              className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
                activeTab === 'info'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <User className="w-4 h-4 mr-2" />
              設備信息
            </button>
            <button
              onClick={() => setActiveTab('events')}
              className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
                activeTab === 'events'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <History className="w-4 h-4 mr-2" />
              事件記錄
            </button>
          </nav>
        </div>

        {activeTab === 'info' ? (
          <>
            {/* 設備詳情卡片 */}
            <div className="bg-white rounded-lg shadow overflow-hidden mb-6">
              <div
                className="px-6 py-4 border-b border-gray-200 flex justify-between items-center cursor-pointer"
                onClick={() => toggleSection('details')}
              >
                <h2 className="text-xl font-semibold text-gray-800">設備詳情</h2>
                {collapsedSections.details ?
                  <ChevronRight className="w-5 h-5 text-gray-500" /> :
                  <ChevronDown className="w-5 h-5 text-gray-500" />
                }
              </div>

              {!collapsedSections.details && (
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">編號</h3>
                        <p className="mt-1 text-lg font-semibold">{device.code || '未設置'}</p>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium text-gray-500">MAC 地址</h3>
                        <p className="mt-1">{device.macAddress}</p>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium text-gray-500">尺寸</h3>
                        <p className="mt-1">{device.data?.size || '未指定'}</p>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium text-gray-500">顏色類型</h3>
                        <div className="mt-1 w-28 h-4">
                          <ColorTypeGradient colorType={device.data?.colorType || '未指定'} size="md" />
                        </div>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium text-gray-500">資料 ID</h3>
                        <p className="mt-1">{device.dataId || '未綁定'}</p>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium text-gray-500">模板名稱</h3>
                        <p className="mt-1">{templateName || '未綁定'}</p>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">備註</h3>
                        <p className="mt-1">{device.note || '無'}</p>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium text-gray-500">狀態</h3>
                        <p className="mt-1">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            device.status === 'online' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {device.status === 'online' ? '在線' : '離線'}
                          </span>
                        </p>
                      </div>

                      {/* 圖片更新狀態已移至預覽圖區塊 */}

                      <div>
                        <h3 className="text-sm font-medium text-gray-500">電量</h3>
                        <p className="mt-1">{renderBatteryIndicator(device.data?.battery)}</p>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium text-gray-500">信號強度</h3>
                        <p className="mt-1">{renderSignalStrength(device.data?.rssi)}</p>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium text-gray-500">最後在線時間</h3>
                        <p className="mt-1">
                          {device.lastSeen
                            ? new Date(device.lastSeen).toLocaleString()
                            : '無記錄'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>            {/* 預覽圖與更新狀態區塊 */}
            <div className="bg-white rounded-lg shadow overflow-hidden mb-6">
              <div
                className="px-6 py-4 border-b border-gray-200 flex justify-between items-center cursor-pointer"
                onClick={() => toggleSection('preview')}
              >
                <h2 className="text-xl font-semibold text-gray-800">預覽圖與更新狀態</h2>
                {collapsedSections.preview ?
                  <ChevronRight className="w-5 h-5 text-gray-500" /> :
                  <ChevronDown className="w-5 h-5 text-gray-500" />
                }
              </div>

              {!collapsedSections.preview && (
                <div className="p-6">
                  <div className="flex flex-col md:flex-row gap-6">
                    {/* 預覽圖區域 */}
                    <div className="flex-1 border rounded-lg overflow-hidden bg-gray-50 flex items-center justify-center" style={{ minHeight: '200px' }}>
                      {(currentDevice?.previewImage || device.previewImage) ? (
                        <img
                          src={currentDevice?.previewImage || device.previewImage}
                          alt="設備預覽圖"
                          className="max-w-full max-h-[300px] object-contain"
                        />
                      ) : (
                        <p className="text-gray-500 text-center p-4">無預覽圖</p>
                      )}
                    </div>

                    {/* 更新狀態區域 */}
                    <div className="flex-1 flex flex-col justify-center space-y-4">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-2">圖片更新狀態</h3>
                        <div className="flex items-center">
                          <ImageUpdateStatusBadge
                            deviceImageCode={currentDevice?.data?.imageCode || device.data?.imageCode}
                            updateStatus={currentDevice?.imageUpdateStatus || device.imageUpdateStatus}
                            size="lg"
                          />
                          <button
                            onClick={handleRefreshDeviceStatus}
                            disabled={isRefreshingStatus}
                            className="ml-3 p-1 text-blue-600 hover:text-blue-800 transition-colors rounded-full hover:bg-blue-100"
                            title="刷新狀態"
                          >
                            <RefreshCw className={`w-5 h-5 ${isRefreshingStatus ? 'animate-spin' : ''}`} />
                          </button>
                        </div>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-2">最後更新時間</h3>
                        <p className="text-sm">
                          {device.updatedAt
                            ? new Date(device.updatedAt).toLocaleString()
                            : '無記錄'}
                        </p>
                      </div>

                      {/* 發送預覽圖按鈕 */}
                      {(currentDevice?.previewImage || device.previewImage) && primaryGateway && (
                        <div className="mt-4">
                          <h3 className="text-sm font-medium text-gray-500 mb-2">發送預覽圖</h3>
                          <div className="flex flex-col sm:flex-row gap-2">
                            <button
                              onClick={() => handleSendPreviewToGateway(false)}
                              disabled={isUpdating || !primaryGateway.status || primaryGateway.status !== 'online'}
                              className={`flex items-center justify-center px-3 py-1.5 rounded-md text-white text-sm transition-colors ${
                                isUpdating || !primaryGateway.status || primaryGateway.status !== 'online'
                                  ? 'bg-gray-400 cursor-not-allowed'
                                  : 'bg-green-500 hover:bg-green-600'
                              }`}
                            >
                              <Send className="w-3.5 h-3.5 mr-1.5" />
                              發送到主要網關
                            </button>

                            {otherGateways.length > 0 && (
                              <button
                                onClick={() => handleSendPreviewToGateway(true)}
                                disabled={isUpdating || !primaryGateway.status || primaryGateway.status !== 'online'}
                                className={`flex items-center justify-center px-3 py-1.5 rounded-md text-white text-sm transition-colors ${
                                  isUpdating || !primaryGateway.status || primaryGateway.status !== 'online'
                                    ? 'bg-gray-400 cursor-not-allowed'
                                    : 'bg-blue-500 hover:bg-blue-600'
                                }`}
                              >
                                <Send className="w-3.5 h-3.5 mr-1.5" />
                                發送到所有網關
                              </button>
                            )}
                          </div>

                          {(!primaryGateway.status || primaryGateway.status !== 'online') && (
                            <p className="mt-2 text-xs text-red-500">
                              主要網關離線，無法發送預覽圖
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* 網關管理區塊 */}
            <div className="bg-white rounded-lg shadow overflow-hidden mb-6">
              <div
                className="px-6 py-4 border-b border-gray-200 flex justify-between items-center cursor-pointer"
                onClick={() => toggleSection('gateway')}
              >
                <h2 className="text-xl font-semibold text-gray-800">網關管理</h2>
                {collapsedSections.gateway ?
                  <ChevronRight className="w-5 h-5 text-gray-500" /> :
                  <ChevronDown className="w-5 h-5 text-gray-500" />
                }
              </div>

              {!collapsedSections.gateway && (
                <div className="p-6">                {/* 網關選擇模式開關 */}                <div className={`mb-6 p-4 rounded-lg relative ${
                  gatewayMode === 'auto' ?
                  'auto-mode-container bg-blue-50' :
                  'bg-blue-50'
                }`}>
                  {gatewayMode === 'auto' && (
                    <>
                      <div className="absolute inset-0 rounded-lg overflow-hidden">
                        <div className="absolute inset-0 flowing-border"></div>
                      </div>
                    </>
                  )}                  <div className="flex items-center justify-between relative z-10">
                    <div>
                      <h3 className={`text-md font-medium ${gatewayMode === 'auto' ? 'text-red-800' : 'text-gray-700'}`}>
                        網關選擇模式
                        {gatewayMode === 'auto' && (
                          <span className="ml-2 inline-flex items-center">
                            <span className="animate-pulse inline-block h-2 w-2 rounded-full bg-red-600 mr-1"></span>
                            <span className="animate-pulse inline-block h-2 w-2 rounded-full bg-red-600"
                                  style={{ animationDelay: '0.5s' }}></span>
                          </span>
                        )}
                      </h3>
                      <p className={`text-sm ${gatewayMode === 'auto' ? 'text-red-700' : 'text-gray-500'}`}>
                        {gatewayMode === 'auto'
                          ? '自動模式：系統將自動選擇最佳網關發送命令'
                          : '手動模式：只允許透過主要網關發送命令'}
                      </p></div>                    <div className="flex items-center">
                      <span className={`mr-2 text-sm ${gatewayMode === 'manual' ? 'font-medium text-blue-600' : 'text-gray-500'}`}>
                        手動
                      </span>                      <button
                        onClick={() => handleToggleGatewayMode(gatewayMode === 'auto' ? 'manual' : 'auto')}
                        disabled={isUpdating}
                        className="relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-red-700 focus:ring-offset-2"
                        style={{ backgroundColor: gatewayMode === 'auto' ? '#B91C1C' : '#E5E7EB' }}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                            gatewayMode === 'auto' ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </button>
                      <span className={`ml-2 text-sm ${gatewayMode === 'auto' ? 'font-medium text-red-700' : 'text-gray-500'}`}>
                        自動
                      </span>
                    </div>
                  </div>
                </div>

                {/* 主要網關 */}
                <div className="mb-6">
                  <h3 className="text-md font-medium text-gray-700 mb-3">主要網關</h3>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    {primaryGateway ? (
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Router className={`w-5 h-5 mr-3 ${primaryGateway.status === 'online' ? 'text-green-500' : 'text-gray-400'}`} />
                          <div>
                            <p className="font-medium">{primaryGateway.name}</p>
                            <p className="text-sm text-gray-500">
                              MAC: {primaryGateway.macAddress} |
                              IP: {primaryGateway.ipAddress} |
                              {primaryGateway.status === 'online' ?
                                <span className="text-green-500"> 在線</span> :
                                <span className="text-gray-500"> 離線</span>}
                            </p>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <p className="text-gray-500">尚未設定主要網關</p>
                    )}
                  </div>
                </div>
                  {/* 其他網關 */}
                <div>
                  <h3 className="text-md font-medium text-gray-700 mb-3">其他網關</h3>

                  {otherGateways.length > 0 ? (
                    <div className="space-y-3">
                      {otherGateways.map(gateway => (
                        <div key={gateway._id} className="flex items-center justify-between bg-gray-50 p-3 rounded-md">
                          <div className="flex items-center">
                            <Router className={`w-5 h-5 mr-3 ${gateway.status === 'online' ? 'text-blue-500' : 'text-gray-400'}`} />
                            <div>
                              <p className="font-medium">{gateway.name}</p>
                              <p className="text-sm text-gray-500">
                                MAC: {gateway.macAddress} |
                                IP: {gateway.ipAddress} |
                                {gateway.status === 'online' ?
                                  <span className="text-green-500"> 在線</span> :
                                  <span className="text-gray-500"> 離線</span>}
                              </p>
                            </div>
                          </div>

                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => {
                                if (gateway._id) {
                                  handleRemoveOtherGateway(gateway._id);
                                }
                              }}
                              disabled={isUpdating || !gateway._id}
                              className="p-1 text-red-600 hover:text-red-800 transition-colors disabled:opacity-50"
                              title="移除網關"
                            >
                              <Trash2 className="w-5 h-5" />
                            </button>

                            <button
                              onClick={() => {
                                if (gateway._id) {
                                  handleSetPrimaryGateway(gateway._id);
                                }
                              }}
                              disabled={isUpdating || !gateway._id}
                              className="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors disabled:opacity-50"
                            >
                              設為主要
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 bg-gray-50 p-4 rounded-lg">沒有其他網關</p>
                  )}
                </div>

                <p className="mt-5 text-sm text-gray-500">
                  <i>主要網關負責此設備的主要控制和通訊，其他網關僅能偵測此設備。</i>
                </p>
              </div>
              )}
            </div>



            {/* 使用者綁定資訊 */}
            <div className="bg-white rounded-lg shadow overflow-hidden mb-6">
              <div
                className="px-6 py-4 border-b border-gray-200 flex justify-between items-center cursor-pointer"
                onClick={() => toggleSection('user')}
              >
                <h2 className="text-xl font-semibold text-gray-800">使用者綁定</h2>
                {collapsedSections.user ?
                  <ChevronRight className="w-5 h-5 text-gray-500" /> :
                  <ChevronDown className="w-5 h-5 text-gray-500" />
                }
              </div>

              {!collapsedSections.user && (
                <div className="p-6">
                  {device.userId ? (
                    <div className="flex items-center">
                      <User className="w-5 h-5 mr-3 text-gray-500" />
                      {userDetail ? (
                        <div>
                          <p className="font-medium">{userDetail.name || userDetail.username}</p>
                          <p className="text-sm text-gray-500">{userDetail.email}</p>
                        </div>
                      ) : (
                        <p className="text-gray-500">正在載入用戶資訊...</p>
                      )}
                    </div>
                  ) : (
                    <p className="text-gray-500">設備尚未綁定使用者</p>
                  )}
                  <p className="mt-4 text-sm text-gray-500">
                    <i>設備在創建時會自動綁定創建者，或在網關發現時自動綁定門店管理員。</i>
                  </p>
                </div>
              )}
            </div>
          </>
        ) : (
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="p-6">
              {device._id && <DeviceEvents deviceId={device._id} />}
            </div>
          </div>
        )}
      </div>

      {/* 回到頂部按鈕 */}
      {showScrollToTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-8 right-8 z-50 p-3 bg-blue-500/40 hover:bg-blue-500 text-white/70 hover:text-white rounded-full shadow-md hover:shadow-xl transition-all duration-300 transform hover:scale-110 backdrop-blur-sm"
          title="回到頂部"
        >
          <ChevronUp className="w-6 h-6" />
        </button>
      )}
    </div>
  );
}
