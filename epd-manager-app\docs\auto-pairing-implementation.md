# EPD Manager App 自動化配對實現指南

## 1. 概述

本文檔詳細說明 EPD Manager App 如何實現自動化配對功能，消除 SERVER 和 GATEWAY 之間手動複製配對的繁瑣流程。

## 2. 當前手動流程分析

### 2.1 現有問題
1. **ws-client-from-copied-info.js** 生成 MAC 地址
2. 用戶需要手動複製 MAC 地址到 Web 端
3. Web 端創建網關後生成 WebSocket 連接資訊
4. 用戶需要手動複製 WebSocket 資訊到網關模擬器
5. 網關模擬器才能開始運行

### 2.2 目標自動化流程
APP 將整個流程自動化，類似 **test-ws-client-interactive.js** 的行為：
1. 用戶登入 APP
2. 選擇門店
3. 點擊「一鍵配對」
4. APP 自動完成所有步驟
5. 開始模擬網關運行

## 3. 核心技術實現

### 3.1 自動化配對服務

```typescript
// services/AutoPairingService.ts
import { generateRandomMac, generateRandomIp } from '../utils/generators';
import { GatewayAPI } from '../api/GatewayAPI';
import { WebSocketService } from './WebSocketService';

export class AutoPairingService {
  private gatewayAPI: GatewayAPI;
  private wsService: WebSocketService;

  constructor() {
    this.gatewayAPI = new GatewayAPI();
    this.wsService = new WebSocketService();
  }

  /**
   * 一鍵自動配對新網關
   * @param storeId 門店ID
   * @param gatewayName 網關名稱（可選）
   */
  async autoCreateAndConnectGateway(storeId: string, gatewayName?: string): Promise<{
    success: boolean;
    gateway?: Gateway;
    error?: string;
  }> {
    try {
      // 步驟1: 生成網關配置（類似 test-ws-client-interactive.js）
      const gatewayConfig = this.generateGatewayConfig(storeId, gatewayName);
      
      // 步驟2: 自動創建網關
      const gateway = await this.gatewayAPI.createGateway(gatewayConfig);
      
      // 步驟3: 自動建立 WebSocket 連接
      const connected = await this.wsService.connectToGateway(gateway, storeId);
      
      if (connected) {
        // 步驟4: 開始模擬網關行為
        this.wsService.startGatewaySimulation(gateway);
        
        return { success: true, gateway };
      } else {
        throw new Error('WebSocket 連接失敗');
      }
    } catch (error) {
      console.error('自動配對失敗:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 生成網關配置（類似 test-ws-client-interactive.js 的邏輯）
   */
  private generateGatewayConfig(storeId: string, gatewayName?: string): GatewayConfig {
    return {
      name: gatewayName || `新網關-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
      macAddress: generateRandomMac(),
      status: 'online',
      model: 'GW-2000',
      wifiFirmwareVersion: '1.0.0',
      btFirmwareVersion: '2.0.0',
      ipAddress: generateRandomIp(),
      storeId: storeId,
      lastSeen: new Date()
    };
  }
}
```

### 3.2 WebSocket 服務

```typescript
// services/WebSocketService.ts
export class WebSocketService {
  private ws: WebSocket | null = null;
  private isConnected: boolean = false;
  private pingInterval: NodeJS.Timeout | null = null;
  private deviceStatusInterval: NodeJS.Timeout | null = null;
  private gatewayInfoInterval: NodeJS.Timeout | null = null;
  private customDevices: Device[] = [];
  private deviceImageCodes: Record<string, string> = {};

  /**
   * 連接到網關（使用服務器返回的 WebSocket 配置）
   */
  async connectToGateway(gateway: Gateway, storeId: string): Promise<boolean> {
    try {
      // 從網關對象中獲取 WebSocket 配置
      const wsConfig = gateway.websocket;
      if (!wsConfig) {
        throw new Error('網關缺少 WebSocket 配置');
      }

      // 構建 WebSocket URL（類似 ws-client-from-copied-info.js）
      const url = `${wsConfig.url}?token=${wsConfig.token}`;
      
      this.ws = new WebSocket(url);
      
      return new Promise((resolve, reject) => {
        this.ws!.onopen = () => {
          console.log('WebSocket 連接已建立');
          this.isConnected = true;
          this.setupMessageHandlers();
          resolve(true);
        };

        this.ws!.onerror = (error) => {
          console.error('WebSocket 連接錯誤:', error);
          reject(false);
        };

        this.ws!.onclose = () => {
          console.log('WebSocket 連接已關閉');
          this.isConnected = false;
          this.cleanup();
        };
      });
    } catch (error) {
      console.error('連接到網關失敗:', error);
      return false;
    }
  }

  /**
   * 開始網關模擬（類似 test-ws-client-interactive.js 的行為）
   */
  startGatewaySimulation(gateway: Gateway): void {
    if (!this.isConnected || !this.ws) {
      throw new Error('WebSocket 未連接');
    }

    // 發送初始 ping 消息
    this.sendPingMessage();

    // 設置定期心跳（25秒間隔）
    this.pingInterval = setInterval(() => {
      this.sendPingMessage();
    }, 25000);

    // 設置定期設備狀態發送（5秒間隔）
    this.deviceStatusInterval = setInterval(() => {
      this.sendDeviceStatusMessage();
    }, 5000);

    // 設置定期網關信息發送（30秒間隔）
    this.gatewayInfoInterval = setInterval(() => {
      this.sendGatewayInfoMessage(gateway);
    }, 30000);
  }

  /**
   * 發送 ping 消息
   */
  private sendPingMessage(): void {
    const pingMessage = {
      type: 'ping',
      timestamp: Date.now()
    };
    this.sendMessage(pingMessage);
  }

  /**
   * 發送設備狀態消息（類似 test-ws-client-interactive.js）
   */
  private sendDeviceStatusMessage(): void {
    // 預設設備（類似 test-ws-client-interactive.js 的預設設備）
    const defaultDevices = this.generateDefaultDevices();
    
    // 合併自定義設備
    const allDevices = [...defaultDevices, ...this.customDevices];

    const deviceStatusMessage = {
      type: 'deviceStatus',
      devices: allDevices
    };

    this.sendMessage(deviceStatusMessage);
  }

  /**
   * 發送網關信息消息
   */
  private sendGatewayInfoMessage(gateway: Gateway): void {
    const gatewayInfoMessage = {
      type: 'gatewayInfo',
      info: {
        macAddress: gateway.macAddress,
        model: gateway.model || 'GW-2000',
        wifiFirmwareVersion: gateway.wifiFirmwareVersion || '1.0.0',
        btFirmwareVersion: gateway.btFirmwareVersion || '2.0.0',
        ipAddress: gateway.ipAddress
      }
    };

    this.sendMessage(gatewayInfoMessage);
  }

  /**
   * 生成預設設備（類似 test-ws-client-interactive.js）
   */
  private generateDefaultDevices(): Device[] {
    return [
      {
        macAddress: this.generateRandomMac(),
        status: 'online',
        data: {
          size: '2.9"',
          battery: Math.floor(Math.random() * 100),
          rssi: -1 * Math.floor(Math.random() * 100),
          colorType: 'BWR',
          imageCode: this.deviceImageCodes[this.generateRandomMac()] || '12345678'
        }
      },
      // 更多預設設備...
    ];
  }

  /**
   * 添加自定義設備（類似 test-ws-client-interactive.js 的 add 命令）
   */
  addCustomDevice(deviceConfig: DeviceConfig): void {
    const customDevice = {
      macAddress: deviceConfig.macAddress,
      status: deviceConfig.status || 'online',
      data: {
        size: deviceConfig.size || '2.9"',
        battery: Math.floor(Math.random() * 100),
        rssi: -1 * Math.floor(Math.random() * 100),
        colorType: deviceConfig.colorType || 'BW'
      }
    };

    this.customDevices.push(customDevice);
    
    // 如果提供了 imageCode，存儲到本地映射
    if (deviceConfig.imageCode) {
      this.deviceImageCodes[deviceConfig.macAddress] = deviceConfig.imageCode;
    }
  }

  /**
   * 移除自定義設備（類似 test-ws-client-interactive.js 的 remove 命令）
   */
  removeCustomDevice(index: number): boolean {
    if (index >= 0 && index < this.customDevices.length) {
      const removedDevice = this.customDevices.splice(index, 1)[0];
      delete this.deviceImageCodes[removedDevice.macAddress];
      return true;
    }
    return false;
  }

  /**
   * 請求設備預覽圖像（類似 test-ws-client-interactive.js 的 request-image 命令）
   */
  requestDeviceImage(macAddress: string): void {
    const requestImageMessage = {
      type: 'requestPreviewImage',
      macAddress: macAddress,
      timestamp: Date.now()
    };

    this.sendMessage(requestImageMessage);
  }

  /**
   * 發送消息到服務器
   */
  private sendMessage(message: any): void {
    if (this.ws && this.isConnected) {
      this.ws.send(JSON.stringify(message));
      console.log('發送消息:', message.type, message);
    }
  }

  /**
   * 設置消息處理器
   */
  private setupMessageHandlers(): void {
    if (!this.ws) return;

    this.ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        this.handleMessage(message);
      } catch (error) {
        console.error('解析消息失敗:', error);
      }
    };
  }

  /**
   * 處理接收到的消息
   */
  private handleMessage(message: any): void {
    console.log('收到消息:', message.type, message);

    switch (message.type) {
      case 'welcome':
        console.log('收到歡迎消息，連接成功');
        break;
      case 'pong':
        console.log('收到 pong 回應');
        break;
      case 'update_preview':
        this.handleImageUpdate(message);
        break;
      default:
        console.log('未知消息類型:', message.type);
    }
  }

  /**
   * 處理圖像更新消息
   */
  private handleImageUpdate(message: any): void {
    if (message.deviceMac && message.imageCode) {
      this.deviceImageCodes[message.deviceMac] = message.imageCode;
      console.log(`更新設備 ${message.deviceMac} 的 imageCode: ${message.imageCode}`);
    }
  }

  /**
   * 清理資源
   */
  private cleanup(): void {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
    if (this.deviceStatusInterval) {
      clearInterval(this.deviceStatusInterval);
      this.deviceStatusInterval = null;
    }
    if (this.gatewayInfoInterval) {
      clearInterval(this.gatewayInfoInterval);
      this.gatewayInfoInterval = null;
    }
  }

  /**
   * 斷開連接
   */
  disconnect(): void {
    this.cleanup();
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.isConnected = false;
  }

  /**
   * 生成隨機 MAC 地址
   */
  private generateRandomMac(): string {
    const hexDigits = "0123456789ABCDEF";
    let mac = "";
    for (let i = 0; i < 6; i++) {
      let part = "";
      for (let j = 0; j < 2; j++) {
        part += hexDigits.charAt(Math.floor(Math.random() * 16));
      }
      mac += part;
      if (i < 5) mac += ":";
    }
    return mac;
  }
}
```

## 4. 使用方式

### 4.1 在 React Native 組件中使用

```typescript
// screens/MainConsoleScreen.tsx
import React, { useState } from 'react';
import { View, Button, Alert } from 'react-native';
import { AutoPairingService } from '../services/AutoPairingService';

export const MainConsoleScreen: React.FC = () => {
  const [isConnecting, setIsConnecting] = useState(false);
  const autoPairingService = new AutoPairingService();

  const handleAutoPairing = async () => {
    setIsConnecting(true);
    
    try {
      const result = await autoPairingService.autoCreateAndConnectGateway(
        selectedStoreId,
        '測試網關'
      );

      if (result.success) {
        Alert.alert('成功', '網關配對成功，已開始模擬運行');
      } else {
        Alert.alert('失敗', result.error || '配對失敗');
      }
    } catch (error) {
      Alert.alert('錯誤', '配對過程中發生錯誤');
    } finally {
      setIsConnecting(false);
    }
  };

  return (
    <View>
      <Button
        title={isConnecting ? "配對中..." : "一鍵配對新網關"}
        onPress={handleAutoPairing}
        disabled={isConnecting}
      />
    </View>
  );
};
```

## 5. 總結

通過這個自動化配對實現，EPD Manager App 成功消除了手動複製的繁瑣流程，實現了：

1. **一鍵操作**：用戶只需點擊一個按鈕
2. **自動創建**：自動生成網關配置並創建
3. **自動連接**：自動建立 WebSocket 連接
4. **自動模擬**：自動開始網關行為模擬
5. **完整功能**：支持設備管理、圖像請求等所有功能

這樣的設計讓 APP 真正成為 SERVER 和 GATEWAY 之間的自動化橋樑。
