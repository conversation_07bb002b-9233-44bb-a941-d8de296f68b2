// EPD Manager App - 自動配對功能測試

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { autoPairingService } from '../services/AutoPairingService';
import { apiService } from '../services/ApiService';
import { webSocketService } from '../services/WebSocketService';
import { COLORS, SIZES } from '../utils/constants';

export const AutoPairingTest: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const testApiConnection = async () => {
    addResult('測試 API 連接...');
    try {
      const result = await apiService.testConnection();
      if (result.success) {
        addResult('✅ API 連接成功');
      } else {
        addResult(`❌ API 連接失敗: ${result.error}`);
      }
    } catch (error: any) {
      addResult(`❌ API 連接錯誤: ${error.message}`);
    }
  };

  const testGatewayConfigGeneration = async () => {
    addResult('測試網關配置生成...');
    try {
      const config = autoPairingService.generateTestGatewayConfig('test-store-id', '測試網關');
      addResult('✅ 網關配置生成成功:');
      addResult(`   名稱: ${config.name}`);
      addResult(`   MAC: ${config.macAddress}`);
      addResult(`   IP: ${config.ipAddress}`);
      addResult(`   型號: ${config.model}`);
    } catch (error: any) {
      addResult(`❌ 網關配置生成失敗: ${error.message}`);
    }
  };

  const testWebSocketService = async () => {
    addResult('測試 WebSocket 服務...');
    try {
      // 測試設備管理功能
      webSocketService.addCustomDevice({
        macAddress: 'AA:BB:CC:DD:EE:FF',
        status: 'online',
        size: '2.9"',
        colorType: 'BWR'
      });
      
      const devices = webSocketService.getDeviceList();
      addResult(`✅ WebSocket 服務正常，設備數量: ${devices.length}`);
      
      // 測試圖像請求
      webSocketService.requestDeviceImage('AA:BB:CC:DD:EE:FF');
      addResult('✅ 圖像請求發送成功');
      
    } catch (error: any) {
      addResult(`❌ WebSocket 服務測試失敗: ${error.message}`);
    }
  };

  const testFullAutoPairing = async () => {
    addResult('開始完整自動配對測試...');
    addResult('⚠️ 注意: 這需要有效的認證和門店 ID');
    
    try {
      // 這裡需要實際的門店 ID，在真實環境中測試
      const testStoreId = 'test-store-id';
      
      addResult('正在驗證門店...');
      const isValidStore = await autoPairingService.validateStore(testStoreId);
      
      if (!isValidStore) {
        addResult('❌ 門店驗證失敗，請使用有效的門店 ID');
        return;
      }
      
      addResult('開始自動配對...');
      const result = await autoPairingService.autoCreateAndConnectGateway(testStoreId, '測試網關');
      
      if (result.success) {
        addResult('✅ 自動配對成功!');
        addResult(`   網關名稱: ${result.gateway?.name}`);
        addResult(`   網關 MAC: ${result.gateway?.macAddress}`);
      } else {
        addResult(`❌ 自動配對失敗: ${result.error}`);
      }
      
    } catch (error: any) {
      addResult(`❌ 自動配對測試錯誤: ${error.message}`);
    }
  };

  const runAllTests = async () => {
    if (isRunning) return;
    
    setIsRunning(true);
    clearResults();
    
    addResult('🚀 開始運行所有測試...');
    
    await testApiConnection();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testGatewayConfigGeneration();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testWebSocketService();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    addResult('✅ 基礎測試完成');
    addResult('💡 如需測試完整自動配對，請確保已登入並選擇門店');
    
    setIsRunning(false);
  };

  const showTestInfo = () => {
    Alert.alert(
      '測試說明',
      '這個測試頁面用於驗證 EPD Manager App 的核心功能：\n\n' +
      '1. API 連接測試\n' +
      '2. 網關配置生成測試\n' +
      '3. WebSocket 服務測試\n' +
      '4. 完整自動配對測試\n\n' +
      '請確保服務器正在運行並且網絡連接正常。',
      [{ text: '確定' }]
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>自動配對功能測試</Text>
        <TouchableOpacity
          style={styles.infoButton}
          onPress={showTestInfo}
        >
          <Text style={styles.infoButtonText}>ℹ️</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, styles.primaryButton]}
          onPress={runAllTests}
          disabled={isRunning}
        >
          <Text style={styles.buttonText}>
            {isRunning ? '測試中...' : '運行基礎測試'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.secondaryButton]}
          onPress={testFullAutoPairing}
          disabled={isRunning}
        >
          <Text style={[styles.buttonText, styles.secondaryButtonText]}>
            測試完整配對
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.clearButton]}
          onPress={clearResults}
          disabled={isRunning}
        >
          <Text style={[styles.buttonText, styles.clearButtonText]}>
            清除結果
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.resultsContainer}>
        <Text style={styles.resultsTitle}>測試結果:</Text>
        <ScrollView style={styles.resultsScroll}>
          {testResults.map((result, index) => (
            <Text key={index} style={styles.resultText}>
              {result}
            </Text>
          ))}
          {testResults.length === 0 && (
            <Text style={styles.emptyText}>
              點擊上方按鈕開始測試
            </Text>
          )}
        </ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
    padding: SIZES.SPACING_MD,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.SPACING_LG,
  },
  title: {
    fontSize: SIZES.FONT_SIZE_XL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  infoButton: {
    padding: SIZES.SPACING_SM,
  },
  infoButtonText: {
    fontSize: SIZES.FONT_SIZE_LG,
  },
  buttonContainer: {
    marginBottom: SIZES.SPACING_LG,
  },
  button: {
    paddingVertical: SIZES.SPACING_MD,
    paddingHorizontal: SIZES.SPACING_LG,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    alignItems: 'center',
    marginBottom: SIZES.SPACING_SM,
  },
  primaryButton: {
    backgroundColor: COLORS.PRIMARY,
  },
  secondaryButton: {
    backgroundColor: COLORS.SURFACE,
    borderWidth: 1,
    borderColor: COLORS.PRIMARY,
  },
  clearButton: {
    backgroundColor: COLORS.WARNING,
  },
  buttonText: {
    fontSize: SIZES.FONT_SIZE_MD,
    fontWeight: '600',
    color: COLORS.SURFACE,
  },
  secondaryButtonText: {
    color: COLORS.PRIMARY,
  },
  clearButtonText: {
    color: COLORS.SURFACE,
  },
  resultsContainer: {
    flex: 1,
    backgroundColor: COLORS.SURFACE,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    padding: SIZES.SPACING_MD,
  },
  resultsTitle: {
    fontSize: SIZES.FONT_SIZE_LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SIZES.SPACING_SM,
  },
  resultsScroll: {
    flex: 1,
  },
  resultText: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 4,
    fontFamily: 'monospace',
  },
  emptyText: {
    fontSize: SIZES.FONT_SIZE_MD,
    color: COLORS.TEXT_DISABLED,
    textAlign: 'center',
    marginTop: SIZES.SPACING_XL,
    fontStyle: 'italic',
  },
});

export default AutoPairingTest;
