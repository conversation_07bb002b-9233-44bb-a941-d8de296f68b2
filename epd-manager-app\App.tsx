// EPD Manager App - 主應用入口

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { useAuth } from './src/stores/authStore';
import { useStores } from './src/stores/storeStore';
import LoginScreen from './src/screens/LoginScreen';
import StoreSelectionScreen from './src/screens/StoreSelectionScreen';
import MainConsoleScreen from './src/screens/MainConsoleScreen';
import { COLORS, SIZES } from './src/utils/constants';

export default function App() {
  const [isInitializing, setIsInitializing] = useState(true);
  const { isAuthenticated, checkAuth, loading: authLoading } = useAuth();
  const { selectedStore, fetchStores } = useStores();

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      console.log('初始化應用...');
      
      // 檢查認證狀態
      const isLoggedIn = await checkAuth();
      
      if (isLoggedIn) {
        // 如果已登入，獲取門店列表
        await fetchStores();
      }
      
    } catch (error) {
      console.error('應用初始化失敗:', error);
      Alert.alert('初始化錯誤', '應用初始化失敗，請重新啟動應用');
    } finally {
      setIsInitializing(false);
    }
  };

  // 顯示加載畫面
  if (isInitializing || authLoading) {
    return (
      <SafeAreaProvider>
        <View style={styles.loadingContainer}>
          <Text style={styles.appTitle}>EPD Manager</Text>
          <ActivityIndicator 
            size="large" 
            color={COLORS.PRIMARY} 
            style={styles.loadingIndicator}
          />
          <Text style={styles.loadingText}>正在初始化...</Text>
        </View>
        <StatusBar style="auto" />
      </SafeAreaProvider>
    );
  }

  // 根據狀態決定顯示哪個畫面
  const renderCurrentScreen = () => {
    if (!isAuthenticated) {
      return <LoginScreen />;
    }
    
    if (!selectedStore) {
      return <StoreSelectionScreen />;
    }
    
    return <MainConsoleScreen />;
  };

  return (
    <SafeAreaProvider>
      <View style={styles.container}>
        {renderCurrentScreen()}
      </View>
      <StatusBar style="auto" />
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND,
    padding: SIZES.SPACING_XL,
  },
  appTitle: {
    fontSize: SIZES.FONT_SIZE_XXL * 1.5,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
    marginBottom: SIZES.SPACING_XL,
    textAlign: 'center',
  },
  loadingIndicator: {
    marginVertical: SIZES.SPACING_LG,
  },
  loadingText: {
    fontSize: SIZES.FONT_SIZE_MD,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
});
