// EPD Manager App - 創建簡單圖標

const fs = require('fs');
const path = require('path');

// 創建簡單的 Base64 編碼 PNG 圖標
const createSimplePNG = (size, color = '#1976D2') => {
  // 這是一個簡單的藍色正方形 PNG 的 Base64 編碼
  // 實際項目中應該使用專業的圖標設計
  const canvas = `
    <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
      <rect width="${size}" height="${size}" fill="${color}" rx="${size * 0.1}"/>
      <text x="50%" y="40%" text-anchor="middle" font-family="Arial" font-size="${size * 0.2}" font-weight="bold" fill="white">EPD</text>
      <text x="50%" y="65%" text-anchor="middle" font-family="Arial" font-size="${size * 0.12}" fill="white">Manager</text>
      <circle cx="${size * 0.85}" cy="${size * 0.15}" r="${size * 0.04}" fill="#4CAF50"/>
    </svg>
  `;
  return canvas;
};

// 確保 assets 目錄存在
const assetsDir = path.join(__dirname, '..', 'assets');
if (!fs.existsSync(assetsDir)) {
  fs.mkdirSync(assetsDir, { recursive: true });
}

try {
  // 創建臨時的 SVG 文件用於轉換
  const iconSVG = createSimplePNG(1024);
  const adaptiveIconSVG = createSimplePNG(1024);
  const splashSVG = `
    <svg width="1242" height="2688" xmlns="http://www.w3.org/2000/svg">
      <rect width="1242" height="2688" fill="#1976D2"/>
      <g transform="translate(621, 1344)">
        <rect x="-200" y="-200" width="400" height="400" fill="white" rx="40"/>
        <text x="0" y="-50" text-anchor="middle" font-family="Arial" font-size="80" font-weight="bold" fill="#1976D2">EPD</text>
        <text x="0" y="20" text-anchor="middle" font-family="Arial" font-size="48" fill="#1976D2">Manager</text>
        <text x="0" y="80" text-anchor="middle" font-family="Arial" font-size="24" fill="#666">自動化橋樑應用</text>
        <circle cx="150" cy="-150" r="20" fill="#4CAF50"/>
      </g>
    </svg>
  `;
  const faviconSVG = createSimplePNG(32);

  // 寫入 SVG 文件
  fs.writeFileSync(path.join(assetsDir, 'icon.svg'), iconSVG);
  fs.writeFileSync(path.join(assetsDir, 'adaptive-icon.svg'), adaptiveIconSVG);
  fs.writeFileSync(path.join(assetsDir, 'splash.svg'), splashSVG);
  fs.writeFileSync(path.join(assetsDir, 'favicon.svg'), faviconSVG);

  console.log('✅ SVG 圖標文件已創建');

  // 創建一個簡單的說明文件，告訴用戶如何轉換為 PNG
  const instructionsContent = `# 圖標轉換說明

## 快速轉換 SVG 到 PNG

### 方法 1: 在線轉換
1. 訪問 https://convertio.co/svg-png/
2. 上傳 SVG 文件
3. 下載 PNG 文件
4. 重命名為對應的文件名

### 方法 2: 使用 VS Code 擴展
1. 安裝 "SVG" 擴展
2. 右鍵點擊 SVG 文件
3. 選擇 "Export PNG"

### 方法 3: 使用在線工具
- https://cloudconvert.com/svg-to-png
- https://www.freeconvert.com/svg-to-png

### 需要的文件
- icon.png (1024x1024)
- adaptive-icon.png (1024x1024) 
- splash.png (1242x2688)
- favicon.png (32x32)

### 臨時解決方案
如果無法轉換 SVG，可以暫時使用純色圖片：
1. 創建 1024x1024 的藍色圖片作為 icon.png
2. 創建相同的圖片作為 adaptive-icon.png
3. 創建 1242x2688 的藍色圖片作為 splash.png
4. 創建 32x32 的藍色圖片作為 favicon.png
`;

  fs.writeFileSync(path.join(assetsDir, 'CONVERSION_INSTRUCTIONS.md'), instructionsContent);

  console.log('✅ 轉換說明已創建');
  console.log('\n📝 下一步：');
  console.log('1. 將 assets/ 目錄中的 SVG 文件轉換為 PNG');
  console.log('2. 或者使用任何 1024x1024 的圖片作為臨時圖標');
  console.log('3. 確保文件名正確：icon.png, adaptive-icon.png, splash.png, favicon.png');

} catch (error) {
  console.error('❌ 創建圖標時發生錯誤:', error);
  process.exit(1);
}
