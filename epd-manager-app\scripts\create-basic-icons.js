const fs = require('fs');
const path = require('path');

// Create a simple SVG icon
const createSVGIcon = (size, filename) => {
  const svg = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <rect width="${size}" height="${size}" fill="#1976D2"/>
  <rect x="${size * 0.2}" y="${size * 0.2}" width="${size * 0.6}" height="${size * 0.6}" fill="white" rx="${size * 0.1}"/>
  <text x="${size * 0.5}" y="${size * 0.6}" font-family="Arial, sans-serif" font-size="${size * 0.3}" fill="#1976D2" text-anchor="middle" font-weight="bold">E</text>
</svg>`;
  
  fs.writeFileSync(path.join(__dirname, '..', 'assets', filename), svg);
  console.log(`Created ${filename}`);
};

// Create SVG files
createSVGIcon(1024, 'icon.svg');
createSVGIcon(1024, 'adaptive-icon.svg');
createSVGIcon(1024, 'splash.svg');
createSVGIcon(32, 'favicon.svg');

console.log('All SVG icons created successfully!');
