{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "start": "node index.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@google/generative-ai": "^0.24.1", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.9", "@types/js-cookie": "^3.0.6", "@types/qrcode": "^1.5.5", "bcryptjs": "^3.0.2", "canvas": "^3.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "express": "^5.1.0", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "jsbarcode": "^3.11.6", "jsdom": "^26.1.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.15.0", "multer": "^1.4.5-lts.2", "node-fetch": "^2.7.0", "node-schedule": "^2.1.1", "puppeteer": "^24.9.0", "qrcode": "^1.5.4", "react-router-dom": "^7.5.2", "svg-pathdata": "^7.2.0", "tailwind-merge": "^3.2.0", "ws": "^8.18.2"}, "devDependencies": {"jest": "^29.7.0", "mongodb-memory-server": "^9.5.0", "supertest": "^7.1.0"}}