// EPD Manager App - 門店選擇頁面

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  TextInput,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../stores/authStore';
import { useStores } from '../stores/storeStore';
import { Store } from '../types';
import { COLORS, SIZES } from '../utils/constants';

export const StoreSelectionScreen: React.FC = () => {
  const [searchText, setSearchText] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  
  const { user, logout } = useAuth();
  const { 
    stores, 
    loading, 
    error, 
    fetchStores, 
    selectStore, 
    clearError 
  } = useStores();

  useEffect(() => {
    loadStores();
  }, []);

  useEffect(() => {
    if (error) {
      Alert.alert('錯誤', error, [
        {
          text: '確定',
          onPress: clearError,
        },
      ]);
    }
  }, [error]);

  const loadStores = async () => {
    try {
      await fetchStores();
    } catch (err) {
      console.error('載入門店列表失敗:', err);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadStores();
    setRefreshing(false);
  };

  const handleStoreSelect = (store: Store) => {
    Alert.alert(
      '確認選擇',
      `確定要選擇門店「${store.name}」嗎？`,
      [
        {
          text: '取消',
          style: 'cancel',
        },
        {
          text: '確定',
          onPress: () => {
            selectStore(store);
            console.log('選擇門店:', store.name);
            // 選擇門店後，App.tsx 會自動跳轉到主控制台
          },
        },
      ]
    );
  };

  const handleLogout = () => {
    Alert.alert(
      '確認登出',
      '確定要登出嗎？',
      [
        {
          text: '取消',
          style: 'cancel',
        },
        {
          text: '登出',
          onPress: logout,
        },
      ]
    );
  };

  // 過濾門店列表
  const filteredStores = stores.filter(store =>
    store.name.toLowerCase().includes(searchText.toLowerCase()) ||
    store.address.toLowerCase().includes(searchText.toLowerCase())
  );

  const renderStoreItem = ({ item }: { item: Store }) => (
    <TouchableOpacity
      style={styles.storeCard}
      onPress={() => handleStoreSelect(item)}
      activeOpacity={0.7}
    >
      <View style={styles.storeCardHeader}>
        <Text style={styles.storeName}>{item.name}</Text>
        <View style={[
          styles.statusBadge,
          item.status === 'active' ? styles.statusActive : styles.statusInactive
        ]}>
          <Text style={styles.statusText}>
            {item.status === 'active' ? '營業中' : '暫停營業'}
          </Text>
        </View>
      </View>
      
      <Text style={styles.storeAddress}>{item.address}</Text>
      
      {item.phone && (
        <Text style={styles.storePhone}>電話: {item.phone}</Text>
      )}
      
      <View style={styles.storeCardFooter}>
        <Text style={styles.storeId}>ID: {item.id || item._id}</Text>
        <Text style={styles.selectHint}>點擊選擇 →</Text>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyList = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyIcon}>🏪</Text>
      <Text style={styles.emptyTitle}>
        {searchText ? '沒有找到匹配的門店' : '沒有可用的門店'}
      </Text>
      <Text style={styles.emptySubtitle}>
        {searchText 
          ? '請嘗試其他搜索關鍵詞' 
          : '請聯繫管理員添加門店'
        }
      </Text>
      {searchText && (
        <TouchableOpacity
          style={styles.clearSearchButton}
          onPress={() => setSearchText('')}
        >
          <Text style={styles.clearSearchButtonText}>清除搜索</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* 頭部 */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.title}>選擇門店</Text>
          <Text style={styles.subtitle}>歡迎，{user?.username}</Text>
        </View>
        
        <TouchableOpacity
          style={styles.logoutButton}
          onPress={handleLogout}
        >
          <Text style={styles.logoutButtonText}>登出</Text>
        </TouchableOpacity>
      </View>

      {/* 搜索框 */}
      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          value={searchText}
          onChangeText={setSearchText}
          placeholder="搜索門店名稱或地址..."
          placeholderTextColor={COLORS.TEXT_DISABLED}
          clearButtonMode="while-editing"
        />
      </View>

      {/* 門店列表 */}
      <FlatList
        data={filteredStores}
        renderItem={renderStoreItem}
        keyExtractor={(item) => item._id || item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[COLORS.PRIMARY]}
          />
        }
        ListEmptyComponent={renderEmptyList}
        showsVerticalScrollIndicator={false}
      />

      {/* 底部統計信息 */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          共 {filteredStores.length} 個門店
          {searchText && ` (搜索結果)`}
        </Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SIZES.SPACING_MD,
    backgroundColor: COLORS.SURFACE,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.TEXT_DISABLED,
  },
  headerLeft: {
    flex: 1,
  },
  title: {
    fontSize: SIZES.FONT_SIZE_XL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  subtitle: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
    marginTop: 2,
  },
  logoutButton: {
    backgroundColor: COLORS.ERROR,
    paddingHorizontal: SIZES.SPACING_MD,
    paddingVertical: SIZES.SPACING_SM,
    borderRadius: SIZES.BORDER_RADIUS_SM,
  },
  logoutButtonText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_SM,
    fontWeight: '600',
  },
  searchContainer: {
    padding: SIZES.SPACING_MD,
    backgroundColor: COLORS.SURFACE,
  },
  searchInput: {
    backgroundColor: COLORS.BACKGROUND,
    borderWidth: 1,
    borderColor: COLORS.TEXT_DISABLED,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    paddingHorizontal: SIZES.SPACING_MD,
    paddingVertical: SIZES.SPACING_SM,
    fontSize: SIZES.FONT_SIZE_MD,
    color: COLORS.TEXT_PRIMARY,
  },
  listContainer: {
    padding: SIZES.SPACING_MD,
    flexGrow: 1,
  },
  storeCard: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    padding: SIZES.SPACING_MD,
    marginBottom: SIZES.SPACING_MD,
    borderWidth: 1,
    borderColor: COLORS.TEXT_DISABLED,
    elevation: 2,
    shadowColor: COLORS.TEXT_PRIMARY,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  storeCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.SPACING_SM,
  },
  storeName: {
    fontSize: SIZES.FONT_SIZE_LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    flex: 1,
    marginRight: SIZES.SPACING_SM,
  },
  statusBadge: {
    paddingHorizontal: SIZES.SPACING_SM,
    paddingVertical: SIZES.SPACING_XS,
    borderRadius: SIZES.BORDER_RADIUS_SM,
  },
  statusActive: {
    backgroundColor: COLORS.SUCCESS,
  },
  statusInactive: {
    backgroundColor: COLORS.WARNING,
  },
  statusText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_XS,
    fontWeight: '600',
  },
  storeAddress: {
    fontSize: SIZES.FONT_SIZE_MD,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SIZES.SPACING_XS,
  },
  storePhone: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SIZES.SPACING_SM,
  },
  storeCardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: SIZES.SPACING_SM,
    borderTopWidth: 1,
    borderTopColor: COLORS.TEXT_DISABLED,
  },
  storeId: {
    fontSize: SIZES.FONT_SIZE_XS,
    color: COLORS.TEXT_DISABLED,
    fontFamily: 'monospace',
  },
  selectHint: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.PRIMARY,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SIZES.SPACING_XL * 2,
  },
  emptyIcon: {
    fontSize: SIZES.FONT_SIZE_XXL * 2,
    marginBottom: SIZES.SPACING_LG,
  },
  emptyTitle: {
    fontSize: SIZES.FONT_SIZE_LG,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    textAlign: 'center',
    marginBottom: SIZES.SPACING_SM,
  },
  emptySubtitle: {
    fontSize: SIZES.FONT_SIZE_MD,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    marginBottom: SIZES.SPACING_LG,
  },
  clearSearchButton: {
    backgroundColor: COLORS.INFO,
    paddingHorizontal: SIZES.SPACING_LG,
    paddingVertical: SIZES.SPACING_SM,
    borderRadius: SIZES.BORDER_RADIUS_MD,
  },
  clearSearchButtonText: {
    color: COLORS.SURFACE,
    fontSize: SIZES.FONT_SIZE_SM,
    fontWeight: '600',
  },
  footer: {
    padding: SIZES.SPACING_MD,
    backgroundColor: COLORS.SURFACE,
    borderTopWidth: 1,
    borderTopColor: COLORS.TEXT_DISABLED,
    alignItems: 'center',
  },
  footerText: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
  },
});

export default StoreSelectionScreen;
