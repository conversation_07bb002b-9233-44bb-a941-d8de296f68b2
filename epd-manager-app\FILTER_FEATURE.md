# 過濾功能實現說明

## 概述
為網關管理和設備管理頁面添加了過濾選單功能，用戶可以通過點選不同的類別標籤來快速過濾和查看對應的設備或網關。

## 功能特點

### 網關管理頁面過濾
- **全部**: 顯示所有網關
- **在線**: 只顯示在線狀態的網關
- **離線**: 只顯示離線狀態的網關
- 每個標籤顯示對應類別的數量

### 設備管理頁面過濾
- **全部**: 顯示所有設備
- **在線**: 只顯示在線狀態的設備
- **離線**: 只顯示離線狀態的設備
- 每個標籤顯示對應類別的數量

## 技術實現

### 新增組件
- `FilterTabs.tsx`: 可重用的過濾標籤組件

### 修改的文件
- `GatewayManagementScreen.tsx`: 添加網關過濾功能
- `DeviceManagementScreen.tsx`: 添加設備過濾功能

### 核心功能
1. **過濾邏輯**: 使用 `useMemo` 計算過濾後的數據列表
2. **動態計數**: 實時計算每個類別的數量
3. **狀態管理**: 使用 `useState` 管理當前選中的過濾條件
4. **UI更新**: 根據過濾結果更新列表顯示

## 使用方法

### FilterTabs 組件
```tsx
import { FilterTabs, FilterOption } from '../components/FilterTabs';

const filterOptions: FilterOption[] = [
  { key: 'all', label: '全部', count: 10 },
  { key: 'online', label: '在線', count: 7 },
  { key: 'offline', label: '離線', count: 3 },
];

<FilterTabs
  options={filterOptions}
  selectedFilter={selectedFilter}
  onFilterChange={setSelectedFilter}
  style={styles.filterContainer}
/>
```

### 過濾邏輯實現
```tsx
const filteredItems = useMemo(() => {
  if (selectedFilter === 'all') {
    return items;
  }
  return items.filter(item => item.status === selectedFilter);
}, [items, selectedFilter]);
```

## 樣式設計
- 使用圓角標籤設計
- 選中狀態使用主色調高亮
- 顯示數量徽章
- 響應式佈局適配不同屏幕尺寸

## 測試
- 包含基本的單元測試
- 測試過濾功能和用戶交互

## 未來擴展
- 可以添加更多過濾條件（如設備型號、固件版本等）
- 支持多選過濾
- 添加搜索功能結合過濾
- 保存用戶的過濾偏好設置
