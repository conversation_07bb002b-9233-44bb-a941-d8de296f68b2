// EPD Manager App - Android 構建腳本

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 EPD Manager App - Android 構建腳本');
console.log('=====================================\n');

// 檢查必要文件
const checkRequiredFiles = () => {
  console.log('📋 檢查必要文件...');
  
  const requiredFiles = [
    'app.json',
    'package.json',
    'eas.json',
    'assets/icon.png',
    'assets/adaptive-icon.png',
    'assets/splash.png'
  ];
  
  const missingFiles = [];
  
  requiredFiles.forEach(file => {
    if (!fs.existsSync(path.join(__dirname, '..', file))) {
      missingFiles.push(file);
    }
  });
  
  if (missingFiles.length > 0) {
    console.error('❌ 缺少必要文件:');
    missingFiles.forEach(file => console.error(`   - ${file}`));
    return false;
  }
  
  console.log('✅ 所有必要文件都存在');
  return true;
};

// 檢查依賴
const checkDependencies = () => {
  console.log('\n📦 檢查依賴...');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'package.json'), 'utf8'));
    
    const requiredDeps = [
      'expo',
      'react',
      'react-native',
      'expo-build-properties'
    ];
    
    const missingDeps = requiredDeps.filter(dep => 
      !packageJson.dependencies[dep] && !packageJson.devDependencies[dep]
    );
    
    if (missingDeps.length > 0) {
      console.error('❌ 缺少必要依賴:');
      missingDeps.forEach(dep => console.error(`   - ${dep}`));
      return false;
    }
    
    console.log('✅ 所有必要依賴都已安裝');
    return true;
  } catch (error) {
    console.error('❌ 檢查依賴時發生錯誤:', error.message);
    return false;
  }
};

// 運行預構建檢查
const runPreBuildChecks = () => {
  console.log('\n🔍 運行預構建檢查...');
  
  try {
    // 檢查 Expo CLI
    execSync('expo --version', { stdio: 'pipe' });
    console.log('✅ Expo CLI 可用');
    
    // 檢查 EAS CLI
    execSync('eas --version', { stdio: 'pipe' });
    console.log('✅ EAS CLI 可用');
    
    return true;
  } catch (error) {
    console.error('❌ 預構建檢查失敗:', error.message);
    return false;
  }
};

// 構建應用
const buildApp = async (buildType = 'preview') => {
  console.log(`\n🔨 開始構建 Android 應用 (${buildType})...`);
  
  try {
    const buildCommand = `eas build --platform android --profile ${buildType} --non-interactive`;
    console.log(`執行命令: ${buildCommand}`);
    
    execSync(buildCommand, { 
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    });
    
    console.log('\n🎉 Android 應用構建完成！');
    return true;
  } catch (error) {
    console.error('\n❌ 構建失敗:', error.message);
    return false;
  }
};

// 本地構建（如果支持）
const buildLocal = () => {
  console.log('\n🏠 嘗試本地構建...');
  
  try {
    // 檢查是否有 Android SDK
    const androidHome = process.env.ANDROID_HOME || process.env.ANDROID_SDK_ROOT;
    
    if (!androidHome) {
      console.log('⚠️  未檢測到 Android SDK，跳過本地構建');
      return false;
    }
    
    console.log('✅ 檢測到 Android SDK');
    
    // 嘗試本地構建
    const buildCommand = 'eas build --platform android --profile development --local';
    console.log(`執行命令: ${buildCommand}`);
    
    execSync(buildCommand, { 
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    });
    
    console.log('\n🎉 本地構建完成！');
    return true;
  } catch (error) {
    console.error('\n❌ 本地構建失敗:', error.message);
    console.log('💡 將嘗試雲端構建...');
    return false;
  }
};

// 主函數
const main = async () => {
  try {
    // 運行檢查
    if (!checkRequiredFiles()) {
      process.exit(1);
    }
    
    if (!checkDependencies()) {
      process.exit(1);
    }
    
    if (!runPreBuildChecks()) {
      process.exit(1);
    }
    
    console.log('\n✅ 所有檢查通過，準備構建...');
    
    // 獲取構建類型
    const buildType = process.argv[2] || 'preview';
    console.log(`📱 構建類型: ${buildType}`);
    
    // 如果是開發構建，先嘗試本地構建
    if (buildType === 'development') {
      const localSuccess = buildLocal();
      if (localSuccess) {
        return;
      }
    }
    
    // 雲端構建
    const success = await buildApp(buildType);
    
    if (success) {
      console.log('\n📱 構建完成！');
      console.log('📥 請到 Expo 控制台下載 APK 文件');
      console.log('🌐 https://expo.dev/');
    } else {
      process.exit(1);
    }
    
  } catch (error) {
    console.error('\n❌ 構建過程中發生錯誤:', error.message);
    process.exit(1);
  }
};

// 顯示使用說明
const showUsage = () => {
  console.log('📖 使用說明:');
  console.log('node scripts/build-android.js [構建類型]');
  console.log('');
  console.log('構建類型:');
  console.log('  development  - 開發版本 (支持熱重載)');
  console.log('  preview      - 預覽版本 (默認)');
  console.log('  production   - 生產版本 (用於發布)');
  console.log('');
  console.log('範例:');
  console.log('  node scripts/build-android.js preview');
  console.log('  node scripts/build-android.js production');
};

// 檢查命令行參數
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  showUsage();
  process.exit(0);
}

// 運行主函數
main().catch(error => {
  console.error('❌ 未預期的錯誤:', error);
  process.exit(1);
});
